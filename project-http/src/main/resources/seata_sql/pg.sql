CREATE TABLE undo_log 
(
    id INTEGER NOT NULL GENERATED BY DEFAULT AS IDENTITY, 
    branch_id BIGINT NOT NULL, 
    xid CHARACTER VARYING(100) NOT NULL, 
    context CHARACTER VARYING(128) NOT NULL, 
    rollback_info BYTEA NOT NULL, 
    log_status INTEGER NOT NULL, 
    log_created TIMESTAMP(6) WITHOUT TIME ZONE NOT NULL, 
    log_modified TIMESTAMP(6) WITHOUT TIME ZONE NOT NULL, 
    PRIMARY KEY (id)
);
