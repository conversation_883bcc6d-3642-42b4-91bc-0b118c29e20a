server:
  port: 15360

dispatch:
  open: false

jupyterLab:
  url: http://***********:8888/lab/workspaces/auto-2/tree/S3

xxl:
  job:
    admin:
      addresses:
    executor:
      address:
      appname: sac-Job
      ip:
      port: 19998
      logpath: /data/xxl-job/jobhandler
      logretentiondays: 30
    accessToken:

spring:
  redis:
    database: ${SPRING_REDIS_DATABASE:10}
    host: ${SPRING_REDIS_HOST:*************}
    port: ${SPRING_REDIS_PORT:6379}
    password: ${SPRING_REDIS_PASSWORD:1qazXSW@}
    ssl: ${SPRING_REDIS_SSL:false}
    timeout: ${SPRING_REDIS_TIMEOUT:3000}
    lettuce:
      pool:
        max-active: ${SPRING_REDIS_LETTUCE_POOL_MAX_ACTIVE:10}
        max-idle: ${SPRING_REDIS_LETTUCE_POOL_MAX_IDLE:5}
        min-idle: ${SPRING_REDIS_LETTUCE_POOL_MIN_IDLE:0}
        max-wait: ${SPRING_REDIS_LETTUCE_POOL_MAX_WAIT:3000}
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      #      primary:
      #        url: jdbc:dm://*************:5236?schema=sac_business
      #        username: SYSDBA
      #        password: SYSDBA
      #        driver-class-name: dm.jdbc.driver.DmDriver
      #      second:
      #        url: jdbc:dm://*************:5236?schema=sac_business
      #        username: SYSDBA
      #        password: SYSDBA
      #        driver-class-name: dm.jdbc.driver.DmDriver
      #      third:
      #        url: jdbc:dm://*************:5236?schema=sac_business
      #        username: SYSDBA
      #        password: SYSDBA
      #        driver-class-name: dm.jdbc.driver.DmDriver
      primary:
        url: jdbc:dm://115.190.124.96:5236?schema=MECHANISM
        username: sysdba
        password: strongP@ssW0rd
        driver-class-name: dm.jdbc.driver.DmDriver
      second:
        url: jdbc:dm://115.190.124.96:5236?schema=MECHANISM
        username: sysdba
        password: strongP@ssW0rd
        driver-class-name: dm.jdbc.driver.DmDriver
      third:
        url: jdbc:dm://115.190.124.96:5236?schema=sac_business
        username: sysdba
        password: strongP@ssW0rd
        driver-class-name: dm.jdbc.driver.DmDriver
      fourth:
        url: jdbc:dm://115.190.124.96:5236?schema=mechanism
        username: sysdba
        password: strongP@ssW0rd
        driver-class-name: dm.jdbc.driver.DmDriver
      mysql:
        url: ***************************************************************************************************************************
        username: root
        password: root
        driver-class-name: com.mysql.jdbc.Driver

      initial-size: 5
      max-active: 5
      min-idle: 5
      filters: config,stat,slf4j
      connection-properties: config.decrypt=true;config.decrypt.key=MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAIMvNjcgKPDsgUXNR8Rs9BqZET+5p3hxcoiSrcx7sv09joUcrNnYkvR7JKa+SS/Y9Je+P5Gs0dKzyFPosEM49PcCAwEAAQ==
      filter.config.enabled: true
      test-on-borrow: true
      test-on-return: true
      test-while-idle: true
minio:
  endPoint: http://115.190.124.96:9000
  username: minioadmin
  password: minioadmin
  bucket-name: mechanism