apollo:
  # meta: ${APOLLO_META:http://config-service-url}
  bootstrap:
    enabled: false
    eagerLoad:
      enabled: false
    namespaces: application-dev-base.yml,application-dev-consul.yml,sac-project-provider.yml
    order: 1
app:
  id: sac-platform # 与 Apollo 配置中心中的 AppId 一致
env: DEV # 指定环境

spring:
  cloud:
    consul:
      enabled: false
    nacos:
      discovery:
        server-addr: 115.190.124.96:8848
        #server-addr: 172.27.16.174:30849
        # 填写命名空间ID！！！
        #namespace: ${NACOS_NAMESPACE:029df0a9-8e5a-4b18-a210-da4be2ed5949}
        #group: ${NACOS_GROUP:platform_group}
        service: ${spring.application.name}
        username: nacos
        password: nacos
        enabled: true
      config:
        enabled: true
        server-addr: 115.190.124.96:8848
        #server-addr: 172.27.16.174:30849
        #namespace: ${NACOS_CONFIG_NAMESPACE:029df0a9-8e5a-4b18-a210-da4be2ed5949}
        #group: ${NACOS_GROUP:platform_group}
        #extension-configs[0]:
        #  data-id: application-dev-base.yml
        #  group: common_group
        file-extension: yml
        username: nacos
        password: nacos
