package com.sac.project;


import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.sac.project.configure.redis.RedisUtil;
import com.sac.framework.utils.SpringUtils;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.web.client.RestTemplate;

/**
 * 项目启动类
 */
@EnableAspectJAutoProxy
@EnableTransactionManagement
@EnableDiscoveryClient
@EnableFeignClients(basePackages = {"com.lc.ibps", "com.sac.project"})
@EnableApolloConfig
@SpringBootApplication(scanBasePackages = {"com.lc.ibps", "com.sac.project","com.sac.framework"}, exclude = { DataSourceAutoConfiguration.class })
public class Runner implements CommandLineRunner {


    public static void main(String[] args) {
        SpringApplication.run(Runner.class, args);
    }

    @Override
    public void run(String... args) throws Exception {
    }

    @Bean
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }


    @Bean
    public RedisUtil redisUtil(){
        return new RedisUtil();
    }

    @Bean
    public SpringUtils springUtils() {
        return new SpringUtils();
    }

}
