package com.sac.project.config;

import com.lc.ibps.base.core.util.EnvUtil;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import springfox.documentation.service.VendorExtension;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Configuration
public class SwaggerConfig {

    @Resource
    private Environment env;

    public static final String titleKey = "swagger.title";
    private String title;

    public static final String hostKey = "swagger.host";
    private String host;

    public static final String descriptionKey = "swagger.description";
    private String description;

    public static final String docsKey = "swagger.docs";
    private String docs = "v2/api-docs";

    public static final String disableKey = "swagger.disable";
    private boolean disable = false;

    public static final String versionKey = "app.version";
    private String version;

    public static final String basePackageKey = "swagger.base-package";
    private String basePackage = "com.lc";

    public static final String termsOfServiceUrlKey = "swagger.terms-of-service-url";
    private String termsOfServiceUrl = "http://www.sac-china.com";

    public static final String contactNameKey = "swagger.contact-name";
    private String contactName = "华盾公司";

    public static final String contactEmailKey = "swagger.contact-email";
    private String contactEmail = "<联系邮箱>";

    public static final String contactUrlKey = "swagger.contact-url";
    private String contactUrl = "http://www.sac-china.com";

    public static final String stringExtensionsKey = "swagger.string-extensions";
    private Set<StringVendorExtension> stringExtensions = new HashSet<>();

    public SwaggerConfig() {

    }

    public String getTitle() {
        if(env.containsProperty(titleKey)) {
            return env.getProperty(titleKey, title);
        }
        return title;
    }

    public String getHost() {
        if(env.containsProperty(hostKey)) {
            return env.getProperty(hostKey, host);
        }
        return host;
    }

    public String getDescription() {
        if(env.containsProperty(descriptionKey)) {
            return env.getProperty(descriptionKey, description);
        }
        return description;
    }

    public String getDocs() {
        if(env.containsProperty(docsKey)) {
            return env.getProperty(docsKey, docs);
        }
        return docs;
    }

    public boolean isDisable() {
        if(env.containsProperty(disableKey)) {
            return env.getProperty(disableKey, Boolean.class, disable);
        }
        return disable;
    }

    public String getVersion() {
        if(env.containsProperty(versionKey)) {
            return env.getProperty(versionKey, version);
        }
        return version;
    }

    public String getTermsOfServiceUrl() {
        if(env.containsProperty(termsOfServiceUrlKey)) {
            return env.getProperty(termsOfServiceUrlKey, termsOfServiceUrl);
        }
        return termsOfServiceUrl;
    }

    public String getBasePackage() {
        if(env.containsProperty(basePackageKey)) {
            return env.getProperty(basePackageKey, basePackage);
        }
        return basePackage;
    }

    public String getContactName() {
        if(env.containsProperty(contactNameKey)) {
            return env.getProperty(contactNameKey, contactName);
        }
        return contactName;
    }

    public String getContactEmail() {
        if(env.containsProperty(contactEmailKey)) {
            return env.getProperty(contactEmailKey, contactEmail);
        }
        return contactEmail;
    }

    public String getContactUrl() {
        if(env.containsProperty(contactUrlKey)) {
            return env.getProperty(contactUrlKey, contactUrl);
        }
        return contactUrl;
    }

    public List<StringVendorExtension> getStringExtensions() {
        stringExtensions.add(new StringVendorExtension("appId", env.getProperty("spring.application.name")));
        List<StringVendorExtension> envList = EnvUtil.getObjectProperties(env, stringExtensionsKey, StringVendorExtension.class);
        if(envList != null && !envList.isEmpty()) {
            stringExtensions.addAll(envList);
        }
        return new ArrayList<>(stringExtensions);
    }

    @SuppressWarnings("rawtypes")
    public List<VendorExtension> getExtensions() {
        List<VendorExtension> result = new ArrayList<>();
        if(null == getStringExtensions()) {
            return result;
        }

        for(StringVendorExtension stringExtension : getStringExtensions()) {
            result.add(new springfox.documentation.service.StringVendorExtension(stringExtension.getName(), stringExtension.getValue()));
        }

        return result;
    }

    @Override
    public String toString() {
        return "SwaggerConfig [disable=" + isDisable()  + ", host=" + getHost() + ", version=" + getVersion() + ", basePackage=" + getBasePackage() + "]";
    }

}

class StringVendorExtension implements VendorExtension<String> {

    private String name;
    private String value;

    public StringVendorExtension() {
        super();
    }

    public StringVendorExtension(String name, String value) {
        super();
        this.name = name;
        this.value = value;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setValue(String value) {
        this.value = value;
    }

    @Override
    public String getName() {
        return name;
    }

    @Override
    public String getValue() {
        return value;
    }

}
