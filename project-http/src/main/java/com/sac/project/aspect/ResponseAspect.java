package com.sac.project.aspect;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

/**
 * 切面示例
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2019/08/02 9:56
 */
@Aspect
@Component
public class ResponseAspect {
    // @Around("execution(* com.sac.project.controller..*(..))")
    public Object controllerProcess(ProceedingJoinPoint pjd) throws Throwable {
//        Object result = pjd.proceed();
//        //如果controller不返回结果
//        if (result == null) {
//            return BaseResponse();
//        }
//
//        return BaseResponse.success(result);
//        throw new NotImplementedException();
        return null;
    }
}
