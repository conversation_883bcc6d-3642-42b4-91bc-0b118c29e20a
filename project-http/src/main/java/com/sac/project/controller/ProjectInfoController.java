package com.sac.project.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lc.ibps.api.base.constants.StateEnum;
import com.sac.framework.request.BaseRequest;
import com.sac.framework.response.BaseResponse;
import com.sac.framework.response.PageList;
import com.sac.framework.response.PageResult;
import com.sac.framework.utils.UUIDGeneratorUtils;
import com.sac.project.config.HttpConstants;
import com.sac.project.modules.project.service.*;
import com.sac.project.project.convert.ProjectInfoQueryDTOConverter;
import com.sac.project.project.dto.*;
import com.sac.project.project.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping(HttpConstants.REST_URL_PREFIX + "/v1/projects")
@Api(tags = "工程管理接口")
@Validated
public class ProjectInfoController {
    private final Logger logger = LoggerFactory.getLogger(ProjectInfoController.class);

    @Autowired
    private ProjectInfoService projectService;

    @Autowired
    private ProjectDependencyService projectDependencyService;

    @Autowired
    private ProjectVersionService projectVersionService;

    @Autowired
    private ProjectCanvasService projectCanvasService;

    @Autowired
    private OperatorGroupService operatorGroupService;

    @ApiOperation("分页查询工程列表")
    @PostMapping("/pagination")
    public BaseResponse<PageList<ProjectInfoVO>> queryPage(@RequestBody BaseRequest baseRequest) {
        ProjectInfoQueryDTOConverter queryConvert = new ProjectInfoQueryDTOConverter();
        ProjectInfoQueryDTO convert = queryConvert.convert(baseRequest);
        IPage<ProjectInfoVO> projectVOIPage = projectService.queryPage(convert);
        List<ProjectInfoVO> records = projectVOIPage.getRecords();
        if (records != null && !records.isEmpty()) {
            for (ProjectInfoVO project : records) {
                String version = project.getVersion();
                if (version != null && !version.startsWith("v")) {
                    project.setVersion("v" + version);
                }
            }
        }
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount((int) projectVOIPage.getTotal());
        pageResult.setPage((int) projectVOIPage.getCurrent());
        pageResult.setLimit((int) projectVOIPage.getSize());
        PageList<ProjectInfoVO> pageList = PageList.<ProjectInfoVO>builder()
                .dataResult(records)
                .pageResult(pageResult)
                .build();
        return BaseResponse.<PageList<ProjectInfoVO>>builder()
                .data(pageList)
                .state(StateEnum.SUCCESS.getCode())
                .build();
    }

    @ApiOperation("新建工程")
    @PostMapping("")
    public BaseResponse<String> create(@Valid @RequestBody ProjectInfoSaveDTO saveDTO) {
        try {
            String id = projectService.saveProject(saveDTO);
            return BaseResponse.<String>builder()
                    .data(id)
                    .state(StateEnum.SUCCESS.getCode())
                    .message("工程创建成功")
                    .build();
        } catch (Exception e) {
            return BaseResponse.<String>builder()
                    .cause(e.getMessage())
                    .state(StateEnum.ERROR.getCode())
                    .build();
        }
    }

    @ApiOperation("批量删除工程")
    @DeleteMapping("/batch")
    public BaseResponse<Void> batchDelete(@RequestBody ProjectBatchDeleteDTO deleteDTO) {
        try {
            projectService.batchDelete(deleteDTO.getIds());
            return BaseResponse.<Void>builder()
                    .state(StateEnum.SUCCESS.getCode())
                    .message("工程批量删除成功")
                    .build();
        } catch (Exception e) {
            return BaseResponse.<Void>builder()
                    .cause(e.getMessage())
                    .state(StateEnum.ERROR.getCode())
                    .build();
        }
    }

    @ApiOperation("查询工程详情")
    @GetMapping("/{id}")
    public BaseResponse<ProjectDetailVO> getDetail(@PathVariable String id) {
        try {
            ProjectDetailVO detail = projectService.getDetail(id);
            if (detail != null && detail.getVersion() != null && !detail.getVersion().startsWith("v")) {
                detail.setVersion("v" + detail.getVersion());
            }
            return BaseResponse.<ProjectDetailVO>builder()
                    .data(detail)
                    .state(StateEnum.SUCCESS.getCode())
                    .build();
        } catch (Exception e) {
            return BaseResponse.<ProjectDetailVO>builder()
                    .cause(e.getMessage())
                    .state(StateEnum.ERROR.getCode())
                    .build();
        }
    }


    @ApiOperation(value = "依赖查询", httpMethod = "GET")
    @GetMapping("/{id}/dependency")
    public BaseResponse<List<ProjectDependencyVO>> queryDependency(@PathVariable String id) {
        List<ProjectDependencyVO> result = projectDependencyService.queryDependency(id);
        return BaseResponse.<List<ProjectDependencyVO>>builder()
                .data(result).state(StateEnum.SUCCESS.getCode()).build();
    }

    @ApiOperation(value = "依赖保存", httpMethod = "POST")
    @PostMapping("/{id}/dependency")
    public BaseResponse<String> createDependency(@Valid @RequestBody ProjectDependencySaveDTO dto,
                                                 @PathVariable String id) {
        try {
            dto.setProjectId(id);
            projectDependencyService.saveDependency(dto);
            return BaseResponse.<String>builder().data(StringUtils.EMPTY).state(StateEnum.SUCCESS.getCode()).build();
        } catch (Exception error) {
            return BaseResponse.<String>builder()
                    .data(error.getMessage()).state(StateEnum.ERROR.getCode()).build();
        }
    }

    @ApiOperation(value = "依赖删除", httpMethod = "DELETE")
    @DeleteMapping("/{id}/dependency")
    public BaseResponse<String> deleteDependency(@RequestBody ProjectDependencyDeleteDTO dto,
                                                 @PathVariable String id) {
        try {
            projectDependencyService.deleteDependency(dto.getId(), id);
            return BaseResponse.<String>builder().data(StringUtils.EMPTY).state(StateEnum.SUCCESS.getCode()).build();
        } catch (Exception error) {
            return BaseResponse.<String>builder()
                    .data(error.getMessage()).state(StateEnum.ERROR.getCode()).build();
        }
    }

    @ApiOperation(value = "依赖更新", httpMethod = "PUT")
    @PutMapping("/{id}/dependency")
    public BaseResponse<String> updateDependency(@Valid @RequestBody ProjectDependencySaveDTO dto,
                                                 @PathVariable String id) {
        try {
            dto.setProjectId(id);
            projectDependencyService.saveDependency(dto);
            return BaseResponse.<String>builder().data(StringUtils.EMPTY).state(StateEnum.SUCCESS.getCode()).build();
        } catch (Exception error) {
            return BaseResponse.<String>builder()
                    .data(error.getMessage()).state(StateEnum.ERROR.getCode()).build();
        }
    }

    @ApiOperation("更新工程")
    @PutMapping("/{id}")
    public BaseResponse<Void> update(@PathVariable String id, @Valid @RequestBody ProjectInfoSaveDTO saveDTO) {
        try {
            saveDTO.setId(id);
            projectService.saveProject(saveDTO);
            return BaseResponse.<Void>builder()
                    .state(StateEnum.SUCCESS.getCode())
                    .message("工程更新成功")
                    .build();
        } catch (Exception e) {
            return BaseResponse.<Void>builder()
                    .cause(e.getMessage())
                    .state(StateEnum.ERROR.getCode())
                    .build();
        }
    }

    @ApiOperation("导出工程")
    @GetMapping("/export")
    public ResponseEntity<byte[]> exportProject(@RequestParam String id, @RequestParam String version) {
        try {
            byte[] zipData = projectService.exportProject(id, version);
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", "project_" + id + "_" + version + ".zip");
            headers.setContentLength(zipData.length);
            return new ResponseEntity<>(zipData, headers, HttpStatus.OK);
        } catch (IllegalArgumentException e) {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        } catch (Exception e) {
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @ApiOperation("导入工程")
    @PostMapping("/import")
    public BaseResponse<Object> importProject(@RequestParam String id, @RequestParam String version, @RequestParam("file") MultipartFile file,
                                              HttpServletRequest request) {
        try {
            projectService.importProject(id, version, file);
            return BaseResponse.builder()
                    .state(StateEnum.SUCCESS.getCode())
                    .request(request.getRequestURI())
                    .message("文件导入成功")
                    .build();
        } catch (Exception e) {
            return BaseResponse.<Object>builder()
                    .state(StateEnum.ERROR.getCode())
                    .request(request.getRequestURI())
                    .message("导入失败")
                    .cause(e.getMessage())
                    .build();
        }
    }

    @ApiOperation(value = "工程新建版本")
    @PostMapping("/{projectId}/versions")
    public BaseResponse newVersion(@PathVariable String projectId, @RequestBody NewVersionDTO newVersionDTO) {
        return projectVersionService.newVersion(projectId, newVersionDTO);
    }

    @ApiOperation(value = "工程删除版本")
    @DeleteMapping("/{projectId}/versions/{versionId}")
    public BaseResponse delVersion(@PathVariable String projectId, @PathVariable String versionId) {
        return projectVersionService.delVersion(projectId, versionId);
    }

    @ApiOperation(value = "工程切换版本")
    @GetMapping("/{projectId}/versions/{versionId}")
    public BaseResponse switchVersion(@PathVariable String projectId, @PathVariable String versionId) {
        return projectVersionService.switchVersion(projectId, versionId);
    }

    @ApiOperation(value = "版本列表查询")
    @GetMapping("/{projectId}/versions")
    public BaseResponse<List<ProjectVersionVO>> queryVersions(@PathVariable String projectId) {
        return projectVersionService.queryVersions(projectId);
    }

    @ApiOperation(value = "获取UUID", httpMethod = "GET")
    @GetMapping("/uuid")
    public BaseResponse<String> getUUID() {
        String uuid = UUIDGeneratorUtils.generateUUIDWithoutHyphens();
        return BaseResponse.<String>builder().data(uuid).state(StateEnum.SUCCESS.getCode()).build();
    }

    @ApiOperation(value = "工程画布算子组查询", httpMethod = "GET")
    @GetMapping("/{id}/canvas/operator-group")
    public BaseResponse<List<OperatorTreeVO>> queryOperatorGroup(@PathVariable String id) {
        List<OperatorTreeVO> operatorGroup = operatorGroupService.getOperatorGroup();
        return BaseResponse.<List<OperatorTreeVO>>builder().data(operatorGroup).state(StateEnum.SUCCESS.getCode()).build();
    }

    @ApiOperation(value = "画布组件文件上传", httpMethod = "POST")
    @PostMapping("/{id}/{version}/canvas/{componentId}/file")
    public BaseResponse<String> importComponentFile(@PathVariable String id, @RequestParam("file") MultipartFile file,
                                           @PathVariable String componentId, @PathVariable String version) {
        try {
            String filePath = projectCanvasService.importFile(id, version, componentId, file);
            logger.info("importComponentFile filePath={}", filePath);
            return BaseResponse.<String>builder().data(filePath).state(StateEnum.SUCCESS.getCode()).build();
        } catch (Exception e) {
            return BaseResponse.<String>builder().cause(e.getMessage()).state(StateEnum.ERROR.getCode()).build();
        }
    }

    @ApiOperation(value = "画布组件保存", httpMethod = "POST")
    @PostMapping("/{id}/{version}/canvas/{componentId}")
    public BaseResponse<String> saveComponentFile(@PathVariable String id, @RequestBody ProjectCanvasSaveDTO dto,
                                                 @PathVariable String componentId, @PathVariable String version) {
        try {
            projectCanvasService.saveCanvasComponent(id, version, componentId, dto);
            return BaseResponse.<String>builder().state(StateEnum.SUCCESS.getCode()).build();
        } catch (Exception e) {
            logger.error("saveComponentFile error:{}", e);
            return BaseResponse.<String>builder().cause(e.getMessage()).state(StateEnum.ERROR.getCode()).build();
        }
    }

    @ApiOperation(value = "画布组件提供JupyterLab路径", httpMethod = "GET")
    @PostMapping("/{id}/{version}/canvas/{componentId}/jupyter-lab")
    public BaseResponse<String> getJupyterLabPath(@PathVariable String id, @PathVariable String componentId,
                                                 @PathVariable String version) {
        try {
            String pythonFilePath = projectCanvasService.createBlankPythonFile(id, version, componentId);
            logger.info("getJupyterLabPath pythonFilePath={}", pythonFilePath);
            return BaseResponse.<String>builder().data(pythonFilePath).state(StateEnum.SUCCESS.getCode()).build();
        } catch (Exception e) {
            return BaseResponse.<String>builder().cause(e.getMessage()).state(StateEnum.ERROR.getCode()).build();
        }
    }

    @ApiOperation(value = "画布保存", httpMethod = "POST")
    @PostMapping("/{id}/{version}/canvas")
    public BaseResponse<String> saveCanvas(@PathVariable String id, @RequestBody List<ProjectCanvasSaveDTO> saveList,
                                           @PathVariable String version) {
        try {
            projectCanvasService.saveCanvasInfo(id, saveList, version);
            return BaseResponse.<String>builder().state(StateEnum.SUCCESS.getCode()).build();
        } catch (Exception e) {
            logger.error("saveCanvas error:{}", e);
            return BaseResponse.<String>builder().cause(e.getMessage()).state(StateEnum.ERROR.getCode()).build();
        }
    }

    @ApiOperation(value = "画布查询", httpMethod = "GET")
    @GetMapping("/{id}/{version}/canvas")
    public BaseResponse<List<ProjectCanvasVO>> queryCanvas(@PathVariable String id, @PathVariable String version) {
        try {
            List<ProjectCanvasVO> canvasVOList = projectCanvasService.queryCanvas(id, version);
            return BaseResponse.<List<ProjectCanvasVO>>builder()
                    .data(canvasVOList).state(StateEnum.SUCCESS.getCode()).build();
        } catch (Exception e) {
            return BaseResponse.<List<ProjectCanvasVO>>builder().cause(e.getMessage()).state(StateEnum.ERROR.getCode()).build();
        }
    }

    @ApiOperation(value = "算子文件同步", httpMethod = "POST")
    @PostMapping("/operator/sync")
    public BaseResponse<String> syncOperator(@RequestBody OperatorSyncDTO dto) {
        try {
            projectCanvasService.syncOperator(dto);
            return BaseResponse.<String>builder().state(StateEnum.SUCCESS.getCode()).build();
        } catch (Exception e) {
            return BaseResponse.<String>builder().cause(e.getMessage()).state(StateEnum.ERROR.getCode()).build();
        }
    }

    @ApiOperation(value = "工程新建存档")
    @PostMapping("/{projectId}/versions/{versionId}/snapshots")
    public BaseResponse newSnapshot(@PathVariable String projectId, @PathVariable String versionId) {
        return projectVersionService.newSnapshot(projectId, versionId);
    }

    @ApiOperation(value = "工程查询存档列表")
    @GetMapping("/{projectId}/versions/{versionId}/snapshots")
    public BaseResponse<List<String>> querySnapshots(@PathVariable String projectId, @PathVariable String versionId) {
        return projectVersionService.querySnapshots(projectId, versionId);
    }

    @ApiOperation(value = "工程存档回退")
    @PostMapping("/{projectId}/versions/{versionId}/snapshots/{snapshot}/rollback")
    public BaseResponse rollbackSnapshot(@PathVariable String projectId, @PathVariable String versionId, @PathVariable String snapshot) {
        return projectVersionService.rollbackSnapshot(projectId, versionId, snapshot);
    }

    @ApiOperation(value = "删除", httpMethod = "DELETE")
    @DeleteMapping("/{id}")
    public BaseResponse<String> delete(@PathVariable String id) {
        try {
            projectService.delete(id);
            return BaseResponse.<String>builder().data(StringUtils.EMPTY).state(StateEnum.SUCCESS.getCode()).build();
        } catch (Exception error) {
            return BaseResponse.<String>builder()
                    .cause(error.getMessage()).state(StateEnum.ERROR.getCode()).build();
        }
    }

    @ApiOperation("复制工程")
    @PostMapping("/copy")
    public BaseResponse<String> copy(@Valid @RequestBody ProjectInfoCopyDTO copyDTO) {
        try {
            String id = projectService.copyProject(copyDTO);
            return BaseResponse.<String>builder()
                    .data(id)
                    .state(StateEnum.SUCCESS.getCode())
                    .message("工程复制成功")
                    .build();
        } catch (Exception e) {
            return BaseResponse.<String>builder()
                    .cause(e.getMessage())
                    .state(StateEnum.ERROR.getCode())
                    .build();
        }
    }

    @ApiOperation("所有类别")
    @GetMapping("/category")
    public BaseResponse<List<String>> queryAllCategories() {
        List<String> categories = projectService.findAllDistinctCategories();
        return BaseResponse.<List<String>>builder()
                .data(categories)
                .state(StateEnum.SUCCESS.getCode())
                .build();
    }
}
