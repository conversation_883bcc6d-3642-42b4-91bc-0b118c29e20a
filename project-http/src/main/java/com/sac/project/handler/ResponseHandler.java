package com.sac.project.handler;

import com.sac.framework.exception.IdempotentException;
import com.sac.framework.exception.RtdbValuesException;
import com.sac.framework.response.BaseResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;

/**
 * 统一返回结果异常处理类
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2019/08/02 10:16
 */
@ControllerAdvice
@ResponseBody
public class ResponseHandler {
    private static Logger logger = LoggerFactory.getLogger(ResponseHandler.class);

    @ExceptionHandler({RtdbValuesException.class})
    public ResponseEntity<BaseResponse> otherResponse(RtdbValuesException e, HttpServletRequest request) {
        BaseResponse basicResponse = BaseResponse.builder().message(e.getMessage()).build();
        return new ResponseEntity(basicResponse, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @ExceptionHandler({IllegalArgumentException.class})
    public ResponseEntity<BaseResponse> otherResponse(IllegalArgumentException e, HttpServletRequest request) {
        BaseResponse basicResponse = BaseResponse.builder().message(e.getMessage()).build();
        return new ResponseEntity(basicResponse, HttpStatus.BAD_REQUEST);
    }


    @ExceptionHandler({Exception.class})
    public ResponseEntity<BaseResponse> otherResponse(Exception e, HttpServletRequest request) {
        BaseResponse basicResponse = BaseResponse.builder().message(e.getMessage()).build();
        return new ResponseEntity(basicResponse, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @ExceptionHandler({IdempotentException.class})
    public ResponseEntity<BaseResponse> otherResponse(IdempotentException e) {
        BaseResponse basicResponse = BaseResponse.builder().message(e.getMessage()).build();
        return new ResponseEntity(basicResponse, HttpStatus.INTERNAL_SERVER_ERROR);
    }


}
