<?xml version="1.0"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
            http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.sac</groupId>
        <artifactId>project</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>project-http</artifactId>

    <dependencies>

        <dependency>
            <groupId>com.sac</groupId>
            <artifactId>project-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sac</groupId>
            <artifactId>project-common</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-consul-discovery</artifactId>
       </dependency>

        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.nacos</groupId>
            <artifactId>nacos-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>snakeyaml</artifactId>
                    <groupId>org.yaml</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>

        <!-- log4j2 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-log4j2</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-yaml</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ctrip.framework.apollo</groupId>
            <artifactId>apollo-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sac.auth</groupId>
            <artifactId>authorization-springboot-starter</artifactId>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.sac.tpmonitor</groupId>-->
<!--            <artifactId>tpmonitor-spring-boot-starter</artifactId>-->
<!--            <version>1.0.0</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>io.minio</groupId>
            <artifactId>minio</artifactId>
            <version>7.0.2</version>
        </dependency>

    </dependencies>

    <build>
        <resources>
            <resource>
                <directory>src/main/java</directory>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <includes>
                    <include>**.*</include>
                    <include>**/*.*</include><!-- i18n能读取到 -->
                    <include>**/*/*.*</include>
                </includes>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-archetype-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
<!--                <configuration>-->
<!--                    <source>1.8</source>-->
<!--                    <target>1.8</target>-->
<!--                    <encoding>UTF-8</encoding>-->
<!--                </configuration>-->
                <executions>
                    <execution>
                        <id>copy-dependencies</id>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/lib</outputDirectory>
                            <overWriteReleases>false</overWriteReleases>
                            <overWriteSnapshots>false</overWriteSnapshots>
                            <overWriteIfNewer>true</overWriteIfNewer>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <artifactId>maven-antrun-plugin</artifactId>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>run</goal>
                        </goals>
                        <configuration>
                            <target>
                                <!-- 创建 classes-exclude 和 lib-exclude 目录 -->
                                <mkdir dir="${project.build.directory}/classes-exclude"/>
                                <mkdir dir="${project.build.directory}/lib-exclude"/>
                                <property name="classes.dir" value="${project.build.directory}/classes-exclude"/>
                                <property name="lib.dir" value="${project.build.directory}/lib-exclude"/>

                                <copy todir="${classes.dir}" includeEmptyDirs="false">
                                    <fileset dir="${project.build.directory}/classes">
                                    </fileset>
                                </copy>
                                <copy todir="${lib.dir}">
                                    <fileset dir="${project.build.directory}/lib"/>
                                </copy>

                                <unjar dest="${classes.dir}" overwrite="false">
                                    <patternset>
                                        <include name="**/appd"/>
                                        <include name="**/*.sh"/>
                                        <include name="**/*.bat"/>
                                        <include name="**/readMe"/>

                                        <include name="**/*.xsd"/>
                                        <include name="**/*.xml"/>
                                        <include name="**/*.sql"/>
                                        <include name="**/*.ftl"/>
                                        <include name="**/*.yml"/>
                                        <include name="**/*.txt"/>
                                        <include name="**/*.conf"/>
                                        <include name="**/*.properties"/>
                                        <include name="**/*.setting"/>
                                        <include name="**/*.env"/>

                                        <exclude name="**/*.map.xml"/>
                                        <exclude name="org/activiti/engine/mapping/**/*.xml"/>
                                    </patternset>
                                    <fileset dir="${lib.dir}">
                                        <include name="lc-*.jar"/>
                                        <include name="ibps-*.jar"/>
                                    </fileset>
                                </unjar>

                                <property name="jarname" value="project-http.jar"/>
                                <property name="jarfile.exclude.basedir" value="${project.build.directory}/${project.artifactId}-exclude"/>
                                <property name="jarfile.exclude.name" value="${jarfile.exclude.basedir}/${jarname}"/>

                                <copy todir="${jarfile.exclude.basedir}" includeEmptyDirs="false">
                                    <fileset dir="${classes.dir}">
                                        <exclude name="**/*.class" />
                                    </fileset>
                                </copy>
                                <copy todir="${jarfile.exclude.basedir}/lib">
                                    <fileset dir="${lib.dir}"/>
                                </copy>
                                <pathconvert property="exclude.lib" pathsep=" ">
                                    <mapper>
                                        <chainedmapper>
                                            <flattenmapper />
                                            <globmapper from="*" to="lib/*" />
                                        </chainedmapper>
                                    </mapper>
                                    <fileset dir="${jarfile.exclude.basedir}/lib">
                                        <include name="*.jar" />
                                    </fileset>
                                </pathconvert>
                                <jar destfile="${jarfile.exclude.name}">
                                    <manifest>
                                        <attribute name="Class-Path" value=". ${exclude.lib}"/>
                                        <attribute name="Main-Class" value="com.sac.project.Runner"/>
                                    </manifest>
                                    <fileset dir="${classes.dir}">
                                        <include name="**/*.class" />
                                    </fileset>
                                </jar>

                                <delete includeEmptyDirs="true">
                                    <fileset dir="${classes.dir}"/>
                                    <fileset dir="${lib.dir}"/>
                                    <fileset dir="${project.build.directory}/lib"/>
                                </delete>
                            </target>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

        </plugins>
    </build>
</project>