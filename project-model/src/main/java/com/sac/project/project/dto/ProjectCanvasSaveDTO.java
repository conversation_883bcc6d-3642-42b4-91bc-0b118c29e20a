package com.sac.project.project.dto;

import lombok.Data;

import java.util.List;

/**
 * ProjectCanvasSaveDTO
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2025/8/27 14:22
 */

@Data
public class ProjectCanvasSaveDTO {
    /**
     * 主键ID
     */
    private String id;

    /**
     * 工程ID
     */
    private String projectId;

    /**
     * 版本
     */
    private String version;

    /**
     * 算子ID
     */
    private String operatorId;

    /**
     * 算子名称
     */
    private String operatorName;

    /**
     * 输入输出ID
     */
    private String inputOutputId;

    /**
     * 输入输出名称
     */
    private String inputOutputName;

    /**
     * MinIO存储路径
     */
    private List<String> minioPath;

    /**
     * minio文件夹路径
     */
    private String minioFolderPath;

    /**
     * 链接配置信息
     */
    private String linkConfig;

    /**
     * 节点配置信息
     */
    private String nodeConfig;
}
