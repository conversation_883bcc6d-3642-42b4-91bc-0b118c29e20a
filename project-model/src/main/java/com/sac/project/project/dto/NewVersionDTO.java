package com.sac.project.project.dto;

import com.sac.framework.utils.UUIDGeneratorUtils;
import com.sac.project.project.entity.ProjectVersion;
import lombok.Data;

@Data
public class NewVersionDTO {
    private String category;
    private String deviceType;
    private String developmentMode;

    public ProjectVersion to(String projectId, int version){
        ProjectVersion projectVersion = new ProjectVersion();
        projectVersion.setVersion(version);
        projectVersion.setProjectId(projectId);
        projectVersion.setId(UUIDGeneratorUtils.generateUUIDWithoutHyphens());
        projectVersion.setDevelopmentMode(developmentMode);
        return projectVersion;

    }
}
