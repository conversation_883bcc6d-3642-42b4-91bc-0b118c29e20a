package com.sac.project.project.bo;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sac.project.project.entity.ProjectDependency;
import com.sac.project.project.entity.ProjectVersion;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.beans.BeanUtils;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Data
@Accessors(chain = true)
public class VersionSnapshotBO {
    private String id;
    private String projectId;
    private int version;
    private String developmentMode;
    private String extraInfo;
    private String isDeleted;
    private Date createTime;
    private List<DependencySnapshotBO> dependenciesSnapshot;

    public static VersionSnapshotBO from(ProjectVersion projectVersion, List<DependencySnapshotBO> dependencySnapshotBOList) {
        return from(projectVersion).setDependenciesSnapshot(dependencySnapshotBOList);
    }

    public static VersionSnapshotBO from(ProjectVersion projectVersion) {
        VersionSnapshotBO bo = new VersionSnapshotBO();
        BeanUtils.copyProperties(projectVersion, bo);
        return bo;
    }

    public static VersionSnapshotBO from(String config) {
        try {
            return new ObjectMapper().readValue(config, VersionSnapshotBO.class);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    public ProjectVersion to() {
        ProjectVersion projectVersion = new ProjectVersion();
        BeanUtils.copyProperties(this, projectVersion);
        return projectVersion;
    }
}
