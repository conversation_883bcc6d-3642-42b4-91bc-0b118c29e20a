package com.sac.project.project.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * OperatorTreeVO
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2025/8/26 14:28
 */
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@Data
public class OperatorTreeVO {
    /**
     * 节点
     */
    private String id;

    /**
     * 父节点
     */
    private String parentId;

    private String operatorId;

    /**
     * 节点名称
     */
    private String name;

    /**
     * 是否为算子
     */
    private Boolean isOperator;

    /**
     * 端口
     */
    private NodeModelsVO models;

    /**
     * 子节点
     */
    private List<OperatorTreeVO> children;

    @Builder(toBuilder = true)
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class NodeModelsVO {
        /**
         * 右端口
         */
        private String rightPortId;

        /**
         * 左端口
         */
        private String leftPortId;

        /**
         * 是否存在右端口
         */
        private Boolean hasRightPorts;

        /**
         * 是否存在左端口
         */
        private Boolean hasLeftPorts;

    }

}
