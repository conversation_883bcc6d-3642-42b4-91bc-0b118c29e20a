package com.sac.project.project.vo;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * CodeItemVO
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2025/8/7 13:30
 */

@Data
@AllArgsConstructor
public class CodeItemVO {
    private String code;
    private String name;
    private String preCode;

    public CodeItemVO(String code, String name) {
        this.code = code;
        this.name = name;
    }
}
