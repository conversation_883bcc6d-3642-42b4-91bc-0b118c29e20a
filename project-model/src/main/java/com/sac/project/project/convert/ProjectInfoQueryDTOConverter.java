package com.sac.project.project.convert;

import com.sac.framework.request.*;
import com.sac.project.project.dto.ProjectInfoQueryDTO;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.function.Consumer;

/**
 * 工程开发分页请求转换器
 * <p>
 * 将通用的BaseRequest转换为ProjectInfoQueryDTO类型
 */
@Component
public class ProjectInfoQueryDTOConverter implements RequestConverter<ProjectInfoQueryDTO> {

    private static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    // 字段名常量，用于从parameters中提取对应字段
    private static final String FIELD_PROJECT_NAME = "name";
    private static final String FIELD_PROJECT_CATEGORY = "category";

    @Override
    public ProjectInfoQueryDTO convert(BaseRequest baseRequest) {
        if (baseRequest == null) {
            return new ProjectInfoQueryDTO();
        }

        // 创建目标对象实例
        ProjectInfoQueryDTO reqDTO = new ProjectInfoQueryDTO();

        // 处理分页参数
        Integer pageSize = baseRequest.getRequestPage().getLimit();
        if (pageSize != null) {
            reqDTO.setLimit(pageSize);
        }
        Integer pageNo = baseRequest.getRequestPage().getPageNo();
        if (pageNo != null) {
            reqDTO.setPageNo(pageNo);
        }

        // 处理字段参数（直接使用小驼峰格式的字段名）
        List<FieldParam> parameters = baseRequest.getParameters();
        if (parameters != null && !parameters.isEmpty()) {
            handleStringFieldParam(parameters, FIELD_PROJECT_NAME, reqDTO::setName);
            handleStringFieldParam(parameters, FIELD_PROJECT_CATEGORY, reqDTO::setCategory);
        }

        // 处理排序参数
//        List<SortingParam> sortingParams = baseRequest.getSorts();
//        if (sortingParams != null && !sortingParams.isEmpty()) {
//            ArrayList<SortingField> sortingFields = new ArrayList<>();
//            for (SortingParam sortingParam : sortingParams) {
//                SortingField sortingField = new SortingField();
//                sortingField.setField(convertToUnderline(sortingParam.getField()));
//                sortingField.setOrder(sortingParam.getOrder());
//                sortingFields.add(sortingField);
//            }
//            reqDTO.setSortingFields(sortingFields);
//        }
        return reqDTO;
    }

    /**
     * 处理字符串类型字段
     * @param parameters 参数列表
     * @param fieldName 字段名
     * @param setter 设置方法
     */
    private void handleStringFieldParam(List<FieldParam> parameters, String fieldName, Consumer<String> setter) {
        for (FieldParam param : parameters) {
            String extractedFieldName = RequestConverterUtils.extractFieldName(param.getKey());
            if (fieldName.equals(extractedFieldName)) {
                String value = param.getValue();
                if (value != null) {
                    setter.accept(value);
                }
                break;
            }
        }
    }

    /**
     * 处理整数类型字段
     * @param parameters 参数列表
     * @param fieldName 字段名
     * @param setter 设置方法
     */
    private void handleIntegerFieldParam(List<FieldParam> parameters, String fieldName, Consumer<Integer> setter) {
        for (FieldParam param : parameters) {
            String extractedFieldName = RequestConverterUtils.extractFieldName(param.getKey());
            if (fieldName.equals(extractedFieldName)) {
                String value = param.getValue();
                if (!StringUtils.isEmpty(value)) {
                    try {
                        Integer intValue = Integer.parseInt(value);
                        setter.accept(intValue);
                    } catch (NumberFormatException e) {
                        // 数字解析异常处理
                    }
                }
                break;
            }
        }
    }

    /**
     * 处理日期时间类型字段
     * @param parameters 参数列表
     * @param fieldName 字段名
     * @param setter 设置方法
     */
    private void handleDateTimeFieldParam(List<FieldParam> parameters, String fieldName, Consumer<LocalDateTime> setter) {
        for (FieldParam param : parameters) {
            String extractedFieldName = RequestConverterUtils.extractFieldName(param.getKey());
            if (fieldName.equals(extractedFieldName)) {
                String value = param.getValue();
                if (!StringUtils.isEmpty(value)) {
                    try {
                        LocalDateTime dateTime = LocalDateTime.parse(value, DATETIME_FORMATTER);
                        setter.accept(dateTime);
                    } catch (Exception e) {
                        // 日期解析异常处理
                    }
                }
                break;
            }
        }
    }

    /**
     * 将驼峰命名转换为下划线命名
     * @param camelCaseStr 驼峰命名字符串
     * @return 下划线命名字符串
     */
    private String convertToUnderline(String camelCaseStr) {
        if (StringUtils.isEmpty(camelCaseStr)) {
            return "";
        }
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < camelCaseStr.length(); i++) {
            char ch = camelCaseStr.charAt(i);
            if (Character.isUpperCase(ch)) {
                result.append("_").append(Character.toLowerCase(ch));
            } else {
                result.append(ch);
            }
        }
        return result.toString();
    }
}