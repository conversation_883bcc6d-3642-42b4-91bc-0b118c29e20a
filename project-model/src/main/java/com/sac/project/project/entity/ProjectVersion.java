package com.sac.project.project.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("PROJECT_VERSION")
@Builder
public class ProjectVersion {

    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @TableField("PROJECT_ID")
    private String projectId;

    @TableField("VERSION")
    private int version;

    @TableField("DEVELOPMENT_MODE")
    private String developmentMode;

    @TableField("EXTRA_INFO")
    private String extraInfo;

    @TableField(value = "IS_DELETED", fill = FieldFill.INSERT_UPDATE)
    private String isDeleted;

    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private Date createTime;
}
