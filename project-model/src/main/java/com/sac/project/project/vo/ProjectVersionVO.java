package com.sac.project.project.vo;

import com.sac.project.project.entity.ProjectVersion;
import lombok.Data;

@Data
public class ProjectVersionVO {

    private String versionId;
    private String version;

    public static ProjectVersionVO from(ProjectVersion version){
        ProjectVersionVO vo = new ProjectVersionVO();
        vo.setVersion("v"+version.getVersion());
        vo.setVersionId(version.getId());
        return vo;
    }
}
