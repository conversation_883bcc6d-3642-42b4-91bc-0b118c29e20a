package com.sac.project.project.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * TreeStructOperatorVO
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2025/8/26 14:34
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class TreeStructOperatorVO {

    /**
     * ID
     */
    private String id;

    /**
     * 父节点ID
     */
    private String parentId;

    /**
     * 节点名称
     */
    private String name;

    /**
     * 节点类型
     */
    private String type;

    /**
     * 节点下挂算子
     */
    private List<OperatorVO> list;
}
