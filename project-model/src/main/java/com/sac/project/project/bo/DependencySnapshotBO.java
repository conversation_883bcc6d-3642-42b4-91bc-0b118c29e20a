package com.sac.project.project.bo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.sac.project.project.entity.ProjectDependency;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.beans.BeanUtils;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Data
public class DependencySnapshotBO {
    private String id;
    private String versionId;
    private String relatedId;
    private String name;
    private String relatedType;
    private String version;
    private String versionRelation;
    private String description;
    private Date updateTime;
    private String updater;
    private Date createTime;
    private String creator;

    public static DependencySnapshotBO from(ProjectDependency projectDependency) {
        DependencySnapshotBO bo = new DependencySnapshotBO();
        BeanUtils.copyProperties(projectDependency, bo);
        return bo;
    }

    public static List<DependencySnapshotBO> from(List<ProjectDependency> projectDependencies) {
        return projectDependencies.stream().map(DependencySnapshotBO::from).collect(Collectors.toList());
    }

    public ProjectDependency to() {
        ProjectDependency projectDependency = new ProjectDependency();
        BeanUtils.copyProperties(this, projectDependency);
        return projectDependency;
    }
}
