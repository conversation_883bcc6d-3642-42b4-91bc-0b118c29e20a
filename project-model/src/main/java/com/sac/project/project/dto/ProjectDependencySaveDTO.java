package com.sac.project.project.dto;

import lombok.Data;

/**
 * ProjectDependencySaveDTO
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2025/8/21 19:18
 */

@Data
public class ProjectDependencySaveDTO {
    /**
     * 依赖ID
     */
    private String id;

    /**
     * 工程ID
     */
    private String projectId;

    /**
     * 关联ID（工程ID或算子ID）
     */
    private String relatedId;

    /**
     * 依赖名称
     */
    private String name;

    /**
     * 关联类型：工程/算子
     */
    private String relatedType;

    /**
     * 版本号
     */
    private String version;

    /**
     * 版本关系
     */
    private String versionRelation;

    /**
     * 描述信息
     */
    private String description;
}
