package com.sac.project.project.vo;

import lombok.Data;

/**
 * ProjectCanvasVO
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2025/8/27 15:26
 */

@Data
public class ProjectCanvasVO {
    /**
     * 主键ID
     */
    private String id;

    /**
     * 工程ID
     */
    private String projectId;

    /**
     * 算子ID
     */
    private String operatorId;

    /**
     * MinIO存储路径
     */
    private String minioPath;

    /**
     * minio文件夹路径
     */
    private String minioFolderPath;

    /**
     * 链接配置信息
     */
    private String linkConfig;

    /**
     * 节点配置信息
     */
    private String nodeConfig;
}
