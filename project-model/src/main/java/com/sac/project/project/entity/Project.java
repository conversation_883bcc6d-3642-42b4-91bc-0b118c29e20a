package com.sac.project.project.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.util.Date;

/**
 * 项目实体
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2022/3/14
 */
@Builder(toBuilder = true)
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@TableName("T_B_PROJECT")
public class Project {
    /**
     * ID
     */
    @TableId(value = "ID",type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 项目code(唯一)
     */
    @TableField("PROJECT_CODE")
    private String projectCode;

    /**
     * 项目名称
     */
    @TableField("PROJECT_NAME")
    private String projectName;

    /**
     * 存储类型（FDFS,NAS）
     */
    @TableField("STORE_TYPE")
    private String storeType;

    /**
     * 创建账户
     */
    @TableField("CREATOR")
    private String creator;

    /**
     * 创建者
     */
    @TableField("CREATOR_NAME")
    private String creatorName;

    /**
     * 创建时间
     */
    @TableField("CREATE_TIME")
    private Date createTime;

    /**
     * 是否已删除 0-未删除 1-已删除
     */
    @TableField("DELETE_FLAG")
    private Integer deleteFlag;

    /**
     * 排序
     */
    @TableField("SORT")
    private Float sort;

    /**
     * 缓存类型
     */
    @TableField("CACHE_TYPE")
    private String cacheType;


}
