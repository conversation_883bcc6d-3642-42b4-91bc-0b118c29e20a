package com.sac.project.project.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * ProjectCanvas
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2025/8/27 14:15
 */

@Builder(toBuilder = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("PROJECT_CANVAS")
public class ProjectCanvas {
    /**
     * 主键ID
     */
    @TableId(value = "ID",type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 工程ID
     */
    @TableField("PROJECT_ID")
    private String projectId;

    /**
     * 工程ID
     */
    @TableField("VERSION")
    private String version;

    /**
     * 算子ID
     */
    @TableField("OPERATOR_ID")
    private String operatorId;

    /**
     * 算子名称
     */
    @TableField("OPERATOR_NAME")
    private String operatorName;

    /**
     * 输入输出ID
     */
    @TableField("INPUT_OUTPUT_ID")
    private String inputOutputId;

    /**
     * 输入输出名称
     */
    @TableField("INPUT_OUTPUT_NAME")
    private String inputOutputName;

    /**
     * MinIO存储路径
     */
    @TableField("MINIO_PATH")
    private String minioPath;

    /**
     * minio文件夹路径
     */
    @TableField("MINIO_FOLDER_PATH")
    private String minioFolderPath;

    /**
     * 链接配置信息
     */
    @TableField("LINK_CONFIG")
    private String linkConfig;

    /**
     * 节点配置信息
     */
    @TableField("NODE_CONFIG")
    private String nodeConfig;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 更新人
     */
    @TableField(value = "UPDATER", fill = FieldFill.INSERT_UPDATE)
    private String updater;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 创建人
     */
    @TableField(value = "CREATOR", fill = FieldFill.INSERT)
    private String creator;
}
