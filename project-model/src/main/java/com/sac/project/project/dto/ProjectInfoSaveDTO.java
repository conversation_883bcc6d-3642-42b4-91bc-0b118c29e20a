package com.sac.project.project.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class ProjectInfoSaveDTO {
    private String id;

    @NotBlank(message = "工程名称不能为空")
    private String name;

    private String version;

    @NotBlank(message = "开发模式不能为空")
    private String developmentMode;

    private String deviceType;

    @NotBlank(message = "工程类型不能为空")
    private String category;

    private String visibleScope;

    private String extraInfo;

    private String developmentLanguage;
}
