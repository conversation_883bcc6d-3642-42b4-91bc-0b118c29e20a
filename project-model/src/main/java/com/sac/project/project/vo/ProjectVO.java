package com.sac.project.project.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;

import java.util.Date;

/**
 * 项目vo
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2022/3/14
 */
@Builder(toBuilder = true)
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ProjectVO {
    /**
     * ID
     */
    private String id;

    /**
     * 项目code(唯一)
     */
    private String projectCode;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 存储类型（FDFS,NAS）
     */
    private String storeType;

    /**
     * 权限(角色)
     */
    private String permission;

    /**
     * 创建账户
     */
    private String creator;

    /**
     * 创建者
     */
    private String creatorName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 是否已删除 0-未删除 1-已删除
     */
    private Integer deleteFlag;

    /**
     * 排序
     */
    private Float sort;

    /**
     * 缓存类型
     */
    private String cacheType;


}
