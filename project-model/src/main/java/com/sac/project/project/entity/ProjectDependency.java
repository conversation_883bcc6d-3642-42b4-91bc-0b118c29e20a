package com.sac.project.project.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * ProjectDependency
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2025/8/21 19:02
 */

@Builder(toBuilder = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("PROJECT_DEPENDENCY")
public class ProjectDependency {

    /**
     * 主键ID
     */
    @TableId(value = "ID",type = IdType.ASSIGN_UUID)
    private String id;

    @TableField("PROJECT_ID")
    private String projectId;

    @TableField("VERSION_ID")
    private String versionId;

    /**
     * 关联ID（工程ID或算子ID）
     */
    @TableField("RELATED_ID")
    private String relatedId;

    /**
     * 依赖名称
     */
    @TableField("NAME")
    private String name;

    /**
     * 关联类型：工程/算子
     */
    @TableField("RELATED_TYPE")
    private String relatedType;

    /**
     * 版本号
     */
    @TableField("VERSION")
    private String version;

    /**
     * 版本关系
     */
    @TableField("VERSION_RELATION")
    private String versionRelation;

    /**
     * 描述信息
     */
    @TableField("DESCRIPTION")
    private String description;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 更新人
     */
    @TableField(value = "UPDATER", fill = FieldFill.INSERT_UPDATE)
    private String updater;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 创建人
     */
    @TableField(value = "CREATOR", fill = FieldFill.INSERT)
    private String creator;
}
