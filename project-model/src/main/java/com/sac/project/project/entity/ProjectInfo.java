package com.sac.project.project.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;

import java.util.Date;

/**
 * 工程信息表实体类
 */
@Builder(toBuilder = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("PROJECT_INFO")
public class ProjectInfo {

    /**
     * 工程 ID（主键）
     */
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 工程名称
     */
    @TableField("NAME")
    private String name;

    /**
     * 工程当前版本号
     */
    @TableField("VERSION_ID")
    private String versionId;

    /**
     * 设备类型
     */
    @TableField("DEVICE_TYPE")
    private String deviceType;

    /**
     * 工程类别
     */
    @TableField("CATEGORY")
    private String category;

    /**
     * 可见 / 可用范围
     */
    @TableField("VISIBLE_SCOPE")
    private String visibleScope;

    /**
     * 创建人
     */
    @TableField(value = "CREATOR", fill = FieldFill.INSERT)
    private String creator;

    /**
     * 创建时间，默认当前时间
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新人
     */
    @TableField(value = "UPDATER", fill = FieldFill.INSERT_UPDATE)
    private String updater;

    /**
     * 更新时间，可在更新时触发更新
     */
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 开发语言
     */
    @TableField(value = "DEVELOPMENT_LANGUAGE")
    private String developmentLanguage;

    /**
     * 是否删除
     */
    @TableField(value = "IS_DELETED", fill = FieldFill.INSERT_UPDATE)
    private String isDeleted;
}
