package com.sac.project.project.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.util.Date;

/**
 * ProjectDependencyVO
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2025/8/21 19:11
 */

@Data
public class ProjectDependencyVO {

    /**
     * 主键ID
     */
    @TableId(value = "ID",type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 关联ID（工程ID或算子ID）
     */
    @TableField("RELATED_ID")
    private String relatedId;

    /**
     * 关联类型：工程/算子
     */
    @TableField("RELATED_TYPE")
    private String relatedType;

    /**
     * 依赖名称
     */
    @TableField("NAME")
    private String name;

    /**
     * 版本号
     */
    private String version;

    /**
     * 版本关系
     */
    private String versionRelation;
    private String versionRelationSymbol;

    /**
     * 描述信息
     */
    private String description;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String creator;
}
