package com.sac.project.project.dto;

import lombok.*;

import java.util.List;

@Builder(toBuilder = true)
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ProjectQueryDTO {

    /**
     * 项目code
     */
    private List<String> projectCodes;

    /**
     * 项目名
     */
    private String projectName;

    /**
     * 存储类型（FDFS,NAS）
     */
    private String storeType;

    /**
     * 缓存类型
     */
    private String cacheType;


    /**
     * 当前页
     */
    private Integer pageNo;

    /**
     * 每页显示条数
     */
    private Integer limit;
}
