package com.sac.project.fegin.request;

import lombok.*;

/**
 * All rights Reserved, Designed By www.sac.com
 *
 * <AUTHOR>
 * @version V1.0.0
 * @description 流程业务中响应参数
 * @date 2019/09/03 10:10
 * @copyright 2019 www.sac.com
 * 注意 本内容仅限于国电南自,禁止外泄以及用于其他的商业
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ResponseParameter {

        private String cause;
        private String data;
        private String message;
        private String request;
        private Integer state;
        private Object variables;
}
