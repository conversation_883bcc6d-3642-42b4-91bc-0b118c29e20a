package com.sac.project.fallbackFactory.operator;

import com.sac.project.fallbackFactory.ProxyFallbackFactory;
import com.sac.project.client.OperatorClient;
import com.sac.project.handler.project.OperatorFallbackInvocationHandler;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * DeploymentPlanFallbackFactory
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2025/8/25 14:18
 */
@Component
public class OperatorFallbackFactory implements FallbackFactory<OperatorClient> {
    @Override
    public OperatorClient create(Throwable cause) {
        return ProxyFallbackFactory.create(OperatorClient.class,
                new OperatorFallbackInvocationHandler(OperatorClient.class, cause));
    }
}
