package com.sac.project.fallbackFactory;

import com.lc.ibps.api.base.constants.StateEnum;
import com.lc.ibps.base.core.exception.Assert;
import com.lc.ibps.base.core.exception.BaseException;
import com.sac.project.handler.FallbackInvocationHandler;
import feign.hystrix.FallbackFactory;
import org.springframework.util.ClassUtils;

import java.lang.reflect.Proxy;

public abstract class ProxyFallbackFactory implements FallbackFactory<Object> {

    /**
     * 创建clientType的jdk动态代理,指定自定义的fallback处理器
     *
     * @param clientType
     * @param invocationHandler
     * @return
     */
    @SuppressWarnings("unchecked")
    public static <T> T create(Class<T> clientType, FallbackInvocationHandler invocationHandler) {
        Assert.notNull(clientType, StateEnum.ERROR_PARAMETER_CLIENTTYPE_REQUIRED.getText(),StateEnum.ERROR_PARAMETER_CLIENTTYPE_REQUIRED.getCode());
        Assert.isTrue(clientType.isInterface(), StateEnum.ERROR_PARAMETER_CLIENTTYPE_MUST_INTERFACE.getText(),StateEnum.ERROR_PARAMETER_CLIENTTYPE_MUST_INTERFACE.getCode());

        Assert.notNull(invocationHandler,StateEnum.ERROR_PARAMETER_INVOCATIONHANDLER_REQUIRED.getText(),StateEnum.ERROR_PARAMETER_INVOCATIONHANDLER_REQUIRED.getCode());
        Assert.notNull(invocationHandler.getCause(),StateEnum.ERROR_PARAMETER_INVOCATIONHANDLER_GETCAUSE_REQUIRED.getText(), StateEnum.ERROR_PARAMETER_INVOCATIONHANDLER_GETCAUSE_REQUIRED.getCode());

        return (T) Proxy.newProxyInstance(clientType.getClassLoader(), new Class[] { clientType }, invocationHandler);

    }

    /**
     * 创建clientType的jdk动态代理，提供默认的fallback处理器
     *
     * @param clientType
     * @param cause
     * @return
     */
    public static <T> T create(Class<T> clientType, Throwable cause) {
        Assert.notNull(clientType,StateEnum.ERROR_PARAMETER_CLIENTTYPE_REQUIRED.getText(),StateEnum.ERROR_PARAMETER_CLIENTTYPE_REQUIRED.getCode());
        Assert.notNull(cause,StateEnum.ERROR_PARAMETER_CAUSE_REQUIRED.getText(),StateEnum.ERROR_PARAMETER_CAUSE_REQUIRED.getCode());

        return create(clientType, new FallbackInvocationHandler(clientType, cause));
    }

    @Override
    public Object create(Throwable cause) {
        try {
            String clientClassName = getClientClassName();
            Class<?> clientType = ClassUtils.forName(clientClassName, null);
            return create(clientType, cause);
        } catch (ClassNotFoundException e) {
            throw new BaseException(StateEnum.ERROR_SYSTEM_FALLBACK_NO_FOUND.getCode(),
                    String.format(StateEnum.ERROR_SYSTEM_FALLBACK_NO_FOUND.getText(),getClientClassName()),
                    getClientClassName());
        }

    }

    /**
     * 用于确定当前工厂创建的FeignClient实例类型
     *
     * @return
     */
    protected abstract String getClientClassName();

}
