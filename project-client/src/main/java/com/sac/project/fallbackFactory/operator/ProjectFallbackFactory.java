package com.sac.project.fallbackFactory.operator;

import com.sac.project.client.ProjectClient;
import com.sac.project.fallbackFactory.ProxyFallbackFactory;
import com.sac.project.handler.project.ProjectFallbackInvocationHandler;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * ProjectFallbackFactory
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2025/8/25 14:18
 */
@Component
public class ProjectFallbackFactory implements FallbackFactory<ProjectClient> {
    @Override
    public ProjectClient create(Throwable cause) {
        return ProxyFallbackFactory.create(ProjectClient.class,
                new ProjectFallbackInvocationHandler(ProjectClient.class, cause));
    }
}
