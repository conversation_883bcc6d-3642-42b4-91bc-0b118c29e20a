package com.sac.project.client;

import com.sac.framework.response.BaseResponse;
import com.sac.project.fallbackFactory.operator.OperatorFallbackFactory;
import com.sac.project.project.dto.OperatorSyncDTO;
import com.sac.project.project.vo.TreeStructOperatorVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * ProjectClient
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2025/8/27 16:59
 */
@FeignClient(name = "mechanism-project",
        fallbackFactory = OperatorFallbackFactory.class,
        path = "/api/v1/projects")
public interface ProjectClient {

    @PostMapping("/operator/sync")
    BaseResponse<String> syncOperator(@RequestBody OperatorSyncDTO dto);
}
