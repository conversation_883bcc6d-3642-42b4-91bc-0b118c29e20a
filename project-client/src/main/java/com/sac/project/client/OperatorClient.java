package com.sac.project.client;

import com.sac.framework.response.BaseResponse;
import com.sac.project.fallbackFactory.operator.OperatorFallbackFactory;
import com.sac.project.project.vo.TreeStructOperatorVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;

/**
 * DeploymentPlanClient
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2025/8/25 14:10
 */

@FeignClient(name = "mechanism-operator",
        fallbackFactory = OperatorFallbackFactory.class,
        path = "/api/v1/operator")
public interface OperatorClient {

    @PostMapping("/query")
    BaseResponse<List<TreeStructOperatorVO>> queryOperators();
}
