package com.sac.project.handler;

import com.lc.ibps.api.base.constants.StateEnum;
import com.lc.ibps.base.core.exception.BaseException;
import com.lc.ibps.base.core.util.string.StringUtil;
import com.lc.ibps.cloud.entity.APIResult;
import com.lc.ibps.cloud.factory.APIResultFactory;
import com.netflix.client.ClientException;
import com.netflix.hystrix.HystrixCircuitBreaker;
import com.netflix.hystrix.HystrixCommandKey;
import com.netflix.hystrix.exception.HystrixTimeoutException;
import feign.Feign;
import feign.FeignException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.net.ConnectException;
import java.net.SocketTimeoutException;
import java.util.concurrent.RejectedExecutionException;

public class FallbackInvocation<PERSON>and<PERSON> implements InvocationHandler {

    private static final Logger logger = LoggerFactory.getLogger(FallbackInvocationHandler.class);

    private Class<?> clientType;
    private Throwable cause;

    public FallbackInvocationHandler(Class<?> clientType, Throwable cause) {
        this.clientType = clientType;
        this.cause = cause;
    }

    public Throwable getCause() {
        return cause;
    }

    @Override
    public final Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
        if (method.getDeclaringClass().equals(Object.class)) {
            return method.invoke(proxy, args);
        }

        try {
            CauseReason reason = resolveCauseReason(method);

            logger.error(reason.asString(), this.cause);

            return this.getReturnValue(method, reason);
        } catch (Throwable e) {
            String configKey = Feign.configKey(this.clientType, method);

            logger.error("服务：{}的fallback方式出现异常，{}", configKey, e.getMessage(), e);

            if (e instanceof RuntimeException) {
                throw e;
            }

            throw new BaseException(e);
        }
    }

    public void setCause(Throwable cause) {
        this.cause = cause;
    }

    protected Object getReturnValue(Method method, CauseReason reason) {
        Class<?> returnType = method.getReturnType();
        if (APIResult.class.isAssignableFrom(returnType)) {
            return APIResultFactory.build(returnType, reason.getStatus(), reason.getReasonCode(), reason.getMessage());
        }
        //方法："+ Feign.configKey(this.clientType, method) + "的返回值:" + returnType.getName() + "不合法。
        throw new BaseException(StateEnum.ILLEGAL_METHOD_RETURN_VALUE.getCode(),
                String.format(StateEnum.ILLEGAL_METHOD_RETURN_VALUE.getText(),Feign.configKey(this.clientType, method),returnType.getName()),
                Feign.configKey(this.clientType, method),
                returnType.getName());
    }

    private boolean circuitBreakerIsOpen(Method method) {
        String configKey = Feign.configKey(this.clientType, method);
        HystrixCommandKey key = HystrixCommandKey.Factory.asKey(configKey);
        HystrixCircuitBreaker breaker = HystrixCircuitBreaker.Factory.getInstance(key);

        boolean isOpen = breaker != null ? breaker.isOpen() : false;
        if(logger.isDebugEnabled()) {
            logger.debug("方法：{}，熔断状态：{}！", configKey, isOpen);
        }

        return isOpen;
    }

    private Throwable findCause(Throwable cause) {
        if (cause.getCause() == null) {
            return cause;
        }

        if (cause.getCause().equals(cause)) {
            return cause;
        }

        return this.findCause(cause.getCause());
    }

    private CauseReason resolveCauseReason(Method method) {
        String reasonCode = StringUtil.build(StateEnum.CLIENT_INVOKE_EXCEPTION.getCode(),"");
        String message = cause.getMessage();

        Throwable realCause = this.findCause(this.cause);
        if (realCause instanceof ConnectException) {
            // 连接异常，比如服务器宕机，网络断开
            reasonCode = StringUtil.build(StateEnum.CONNECT_ERROR.getCode(),"");
            message = StateEnum.CONNECT_ERROR.getText();
        } else if (realCause instanceof SocketTimeoutException) {
            // 超时
            reasonCode = StringUtil.build(StateEnum.SOCKET_READ_TIMEOUT.getCode(),"");
            message = StringUtil.build(StateEnum.SOCKET_READ_TIMEOUT.getText(), ":", message);
        } else {
            String realCauseMessage = realCause.getMessage();
            if (realCause instanceof ClientException) {
                // 没有服务提供者
                if (realCauseMessage.startsWith("Load balancer does not have available server for client")) {
                    reasonCode = StringUtil.build(StateEnum.UNAVAILABLE_SERVICE.getCode(), "");

                    String serviceName = realCauseMessage.split(":")[1].trim();
                    message = StringUtil.build("服务", serviceName, "不可用，原因：", message);
                }
            } else if (realCause instanceof HystrixTimeoutException) {
                // 熔断器超时
                reasonCode = StringUtil.build(StateEnum.HYSTRIX_TIMEOUT.getCode(), "");
                message = StateEnum.HYSTRIX_TIMEOUT.getText();
            } else if (realCause instanceof RejectedExecutionException) {
                // 线程池耗尽
                reasonCode = StringUtil.build(StateEnum.HYSTRIX_THREAD_POOL_FULL.getCode(),"");
                message = StateEnum.HYSTRIX_THREAD_POOL_FULL.getText();
            } else if (realCause instanceof FeignException) {
                int status = ((FeignException) realCause).status();

                switch (status) {
                    case 404:
                        // 目标服务404
                        reasonCode = StringUtil.build(StateEnum.REQUEST_404.getCode(), "");

                        message = StateEnum.REQUEST_404.getText();
                        break;
                    default:
                        break;
                }

            } else if (realCause instanceof RuntimeException) {
                // 熔断器是否已经打开
                if (this.circuitBreakerIsOpen(method)) {
                    // 熔断器已打开
                    reasonCode = StringUtil.build(StateEnum.HYSTRIX_CIRCUITBREAKER_OPEN.getCode(), "");
                    message = StateEnum.HYSTRIX_CIRCUITBREAKER_OPEN.getText();
                } else if ("could not acquire a semaphore for execution".equals(realCauseMessage)) {
                    reasonCode = StringUtil.build(StateEnum.HYSTRIX_SEMAPHORE_NOT_ACQUIRED.getCode(), "");
                    message = StateEnum.HYSTRIX_SEMAPHORE_NOT_ACQUIRED.getText();
                }
            }
        }

        String configKey = Feign.configKey(this.clientType, method);
        return new CauseReason(reasonCode, StringUtil.build("服务",configKey,"不可用，原因：", message));
    }

    public static class CauseReason {
        private Integer status = StateEnum.HYSTRIX_FALLBACK.getCode();
        private String reasonCode;
        private String message;

        public CauseReason(String reasonCode, String message) {
            this.reasonCode = reasonCode;
            this.message = message;
        }

        public String asString() {
            return this.toString();
        }

        public String getMessage() {
            return message;
        }

        public String getReasonCode() {
            return reasonCode;
        }

        public Integer getStatus() {
            return status;
        }

        public void setStatus(Integer status) {
            this.status = status;
        }

        @Override
        public String toString() {
            return StringUtil.build(this.reasonCode , "-" , this.message);
        }

    }

}
