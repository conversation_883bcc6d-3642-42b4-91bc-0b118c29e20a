package com.sac.project.handler.project;

import com.lc.ibps.api.base.constants.StateEnum;
import com.lc.ibps.base.core.exception.BaseException;
import com.lc.ibps.cloud.entity.APIResult;
import com.lc.ibps.cloud.factory.APIResultFactory;
import com.sac.framework.response.BaseResponse;
import com.sac.framework.response.BaseResponseFactory;
import com.sac.project.handler.FallbackInvocationHandler;
import feign.Feign;

import java.lang.reflect.Method;

/**
 * ProjectFallbackInvocationHandler
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2025/8/25 14:34
 */
public class ProjectFallbackInvocationHandler extends FallbackInvocationHandler {
    private Class<?> clientType;

    public ProjectFallbackInvocationHandler(Class<?> clientType, Throwable cause) {
        super(clientType, cause);
        this.clientType = clientType;
    }

    @Override
    protected Object getReturnValue(Method method, CauseReason reason) {
        Class<?> returnType = method.getReturnType();
        if (APIResult.class.isAssignableFrom(returnType)) {
            return APIResultFactory.build(returnType, reason.getStatus(), reason.getReasonCode(), reason.getMessage());
        } else if (BaseResponse.class.isAssignableFrom(returnType)) {
            return BaseResponseFactory.build(returnType, reason.getStatus(), reason.getReasonCode(), reason.getMessage());
        } else {
            throw new BaseException(StateEnum.ILLEGAL_METHOD_RETURN_VALUE.getCode(), String.format(StateEnum.ILLEGAL_METHOD_RETURN_VALUE.getText(), Feign.configKey(this.clientType, method), returnType.getName()), new Object[]{Feign.configKey(this.clientType, method), returnType.getName()});
        }
    }
}
