package com.sac.project.configure.datasource.bpp;

import com.alibaba.druid.pool.DruidDataSource;
import com.lc.ibps.base.core.encrypt.EncryptUtil;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class DatasourceHandleProcessor implements BeanPostProcessor {

    @Resource
    private Environment environment;

    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
        if (beanName.equals("primaryDataSource") || beanName.equals("secondDataSource") || beanName.equals("thirdDataSource")) {
            DruidDataSource ds = (DruidDataSource) bean;
            String password = ds.getPassword();
            if (isEncrypt()) {
                password = EncryptUtil.decrypt(password);
                ds.setPassword(password);
            }
            return ds;
        }
        return bean;
    }

//    ffe51a988d35969ca3bfb5710b2a9e66    1qazXSW@
//    cd8fe01e56492ba385e4ac5031bfc344    db2inst1

    /**
     * 是否加密
     * @return
     */
    private boolean isEncrypt() {
        String encrypt = environment.getProperty("db.encrypt", "false").toLowerCase();
        return Boolean.valueOf(encrypt);
    }

}
