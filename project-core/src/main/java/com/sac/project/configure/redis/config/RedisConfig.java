package com.sac.project.configure.redis.config;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lc.ibps.base.core.encrypt.EncryptUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.cache.annotation.CachingConfigurerSupport;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisPassword;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import javax.annotation.Resource;
import java.io.Serializable;

/**
 * @author: wwg
 * @date: 2022/06/20 09:34
 * @description: 配置自动从resources/conf/redis.yml中获取
 * @copyright: 2019 南京华盾电力信息安全测评有限公司 All rights reserved.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EnableCaching
@Configuration
public class RedisConfig  extends CachingConfigurerSupport {

    @Resource
    private Environment environment;

    @Bean(name = "redisConnectionFactory")
    public LettuceConnectionFactory myLettuceConnectionFactory(RedisProperties properties) {
        RedisStandaloneConfiguration config = new RedisStandaloneConfiguration();
        config.setHostName(properties.getHost());
        config.setPort(properties.getPort());
        String password = properties.getPassword();
        if (isEncrypt()) {
            // 如果加过密，需要进行解密
            password = EncryptUtil.decrypt(password);
        }
        config.setPassword(RedisPassword.of(password));
        config.setDatabase(properties.getDatabase());
        return new LettuceConnectionFactory(config);
    }

    @Bean(name = "redisTemplateObj")
    public RedisTemplate<String, Object> redisTemplateObj(LettuceConnectionFactory redisConnectionFactory){
        // 配置redisTemplate
        RedisTemplate<String, Object> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(redisConnectionFactory);
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        objectMapper.enableDefaultTyping(ObjectMapper.DefaultTyping.NON_FINAL);
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.setValueSerializer(new GenericJackson2JsonRedisSerializer(objectMapper));
        // hash的key也采用String的序列化方式
        redisTemplate.setHashKeySerializer(new StringRedisSerializer());
        // hash的value序列化方式采用jackson
        redisTemplate.setHashValueSerializer(new GenericJackson2JsonRedisSerializer(objectMapper));
        redisTemplate.afterPropertiesSet();
        return redisTemplate;
    }

    private Logger logger = LoggerFactory.getLogger(getClass());

    /**
     * Provides the central class of the Redis module for Redis interactions.
     * @return the Redis interaction class
     */
    @Bean
    //@ConditionalOnMissingBean(name = "redisTemplate")
    public RedisTemplate<String, Serializable> redisTemplate(@Qualifier("redisConnectionFactory") RedisConnectionFactory factory) {
        logger.debug("redis template initial.");
        RedisTemplate<String, Serializable> redisTemplate = new RedisTemplate<>();
        redisTemplate.setKeySerializer(new StringRedisSerializer());//key的序列化适配器
        //redisTemplate.setValueSerializer(new GenericJackson2JsonRedisSerializer());
        redisTemplate.setConnectionFactory(factory);
        //RedisUtil.redisTemplate = redisTemplate;
        return redisTemplate;
    }

    /**
     * Provides the central class of the Redis module for Redis interactions.
     * @return the Redis interaction class
     */
    @Bean
    //@ConditionalOnMissingBean(name = "redisTemplateString")
    public RedisTemplate<String, String> redisTemplateString(@Qualifier("redisConnectionFactory") RedisConnectionFactory factory) {
        logger.debug("string redis template initial.");
        RedisTemplate<String, String> redisTemplateString = new RedisTemplate<>();
        redisTemplateString.setKeySerializer(new StringRedisSerializer());//key的序列化适配器
        redisTemplateString.setValueSerializer(new StringRedisSerializer());//value的序列化适配器
        redisTemplateString.setConnectionFactory(factory);

        //RedisUtil.redisTemplateString = redisTemplateString;
        return redisTemplateString;
    }

    /**
     * Provides the central class of the Redis module for Redis interactions.
     * @return the Redis interaction class
     */
    @Bean
    //@ConditionalOnMissingBean(name = "redisTemplateInteger")
    public RedisTemplate<String, Integer> redisTemplateInteger(@Qualifier("redisConnectionFactory") RedisConnectionFactory factory) {
        logger.debug("integer redis template initial.");
        RedisTemplate<String, Integer> redisTemplateInteger = new RedisTemplate<>();
        redisTemplateInteger.setKeySerializer(new StringRedisSerializer());//key的序列化适配器
        redisTemplateInteger.setConnectionFactory(factory);

        //RedisUtil.redisTemplateInredisteger = redisTemplateInteger;
        return redisTemplateInteger;
    }

    /**
     * Provides the central class of the Redis module for Redis interactions.
     * @return the Redis interaction class
     */
    @Bean
    @ConditionalOnMissingBean(name = "stringRedisTemplate")
    public StringRedisTemplate redisTemplateLong(@Qualifier("redisConnectionFactory") RedisConnectionFactory factory) {
        logger.debug("string redis template initial.");
        StringRedisTemplate stringRedisTemplate = new StringRedisTemplate(factory);

        //RedisUtil.stringRedisTemplate = stringRedisTemplate;
        return stringRedisTemplate;
    }


    @Bean
    public RedisCacheManager cacheManager(RedisConnectionFactory factory) {
        return RedisCacheManager.create(factory);
    }

    /**
     * 是否加密
     * @return
     */
    private boolean isEncrypt() {
        String encrypt = environment.getProperty("db.encrypt", "false").toLowerCase();
        return Boolean.valueOf(encrypt);
    }

}
