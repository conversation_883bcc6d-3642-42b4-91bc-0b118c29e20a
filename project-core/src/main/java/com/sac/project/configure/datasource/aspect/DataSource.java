package com.sac.project.configure.datasource.aspect;
import java.lang.annotation.*;

/**
 * All rights Reserved, Designed By www.sac.com
 *
 * <AUTHOR>
 * @version V1.0.0
 * @title DataSource
 * @package com.sac.project.configure.datasource.aspect
 * @description ${TODO}
 * @date 2019/10/23 10:00
 * @copyright 2019 www.sac.com
 * 注意 本内容仅限于国电南自,禁止外泄以及用于其他的商业
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE, ElementType.METHOD})
@Inherited()
public @interface DataSource {

    String value();
}

