package com.sac.project.modules.project.service.impl;

import com.sac.framework.response.BaseResponse;
import com.sac.framework.utils.DateUtils;
import com.sac.framework.utils.ResultUtil;
import com.sac.project.constant.ProjectConstants;
import com.sac.project.modules.project.mapper.ProjectDependencyMapper;
import com.sac.project.modules.project.mapper.ProjectInfoMapper;
import com.sac.project.modules.project.mapper.ProjectVersionMapper;
import com.sac.project.modules.project.service.MinioService;
import com.sac.project.modules.project.service.ProjectVersionService;
import com.sac.project.project.bo.DependencySnapshotBO;
import com.sac.project.project.bo.VersionSnapshotBO;
import com.sac.project.project.dto.NewVersionDTO;
import com.sac.project.project.entity.ProjectDependency;
import com.sac.project.project.entity.ProjectVersion;
import com.sac.project.project.vo.ProjectSnapshotVO;
import com.sac.project.project.vo.ProjectVersionVO;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class ProjectVersionServiceImpl implements ProjectVersionService {

    @Autowired
    ProjectVersionMapper projectVersionMapper;

    @Autowired
    ProjectInfoMapper projectInfoMapper;
    @Autowired
    private ProjectDependencyMapper projectDependencyMapper;

    @Autowired
    MinioService minioService;

    @Override
    public BaseResponse newVersion(String projectId, NewVersionDTO newVersionDTO) {
        projectVersionMapper.newVersion(projectId, newVersionDTO);
        return ResultUtil.success();
    }

    @Override
    public BaseResponse delVersion(String projectId, String versionId) {
        if (projectInfoMapper.isCurrentVersion(projectId, versionId)){
            return ResultUtil.error("无法删除当前版本");
        }
        projectVersionMapper.deleteById(versionId);
        return ResultUtil.success();
    }

    @Override
    public BaseResponse switchVersion(String projectId, String versionId) {

        if (!projectVersionMapper.isVersionExist(versionId)){
            return ResultUtil.error("版本不存在");
        }
        projectInfoMapper.switchVersion(projectId, versionId);
        return ResultUtil.success();
    }

    @Override
    public BaseResponse<List<ProjectVersionVO>> queryVersions(String projectId) {
        List<ProjectVersionVO> list = projectVersionMapper.queryVersionsByProjectId(projectId);
        return ResultUtil.success(list);
    }

    @Override
    public BaseResponse newSnapshot(String projectId, String versionId) {
        ProjectVersion projectVersion = projectVersionMapper.selectById(versionId);
        if (projectVersion == null){
            throw new RuntimeException("版本不存在，无法创建存档");
        }
        List<ProjectDependency>  dependencies = projectDependencyMapper.findVersionDependencies(versionId);

        VersionSnapshotBO versionSnapshotBO = VersionSnapshotBO.from(projectVersion, DependencySnapshotBO.from(dependencies));
        String snapshotName = DateUtils.now_yyyyMMddHHmmss();
        //上传存档的版本配置
        minioService.upload(ProjectConstants.PROJECT_BUCKET_NAME, buildSnapshotConfigFileName(projectId, versionId, snapshotName), versionSnapshotBO.toString());
        //拷贝项目代码
        minioService.copyPath(ProjectConstants.PROJECT_BUCKET_NAME, buildProjectCodePath(projectId, versionId), buildSnapshotCodePath(projectId, versionId, snapshotName));
        return ResultUtil.success();
    }

    private String buildSnapshotPrefix(String projectId, String versionId) {
        return "/project/" + projectId  + "/" + versionId + "/snapshot/";
    }

    private String buildSnapshotPath(String projectId, String versionId, String snapshotName) {
        return buildSnapshotPrefix(projectId, versionId) + snapshotName  + "/";
    }

    private String buildSnapshotCodePath(String projectId, String versionId, String snapshotName) {
        return "/project/" + projectId  + "/" + versionId + "/snapshot/" + snapshotName  + "/code/";
    }

    private String buildProjectCodePath(String projectId, String versionId) {
        return "/project/" + projectId  + "/" + versionId + "/latest/code/";
    }

    private String buildSnapshotConfigFileName(String projectId, String versionId, String snapshotName) {
        return buildSnapshotPath(projectId, versionId, snapshotName) + "snapshot.json";
    }

    @Override
    public BaseResponse<List<String>> querySnapshots(String projectId, String versionId) {
        String prefix = buildSnapshotPrefix(projectId, versionId);
        List<String> snapshots = minioService.listObjectName(ProjectConstants.PROJECT_BUCKET_NAME, buildSnapshotPrefix(projectId, versionId))
                .stream()
                .map(objectName -> objectName.replace(prefix, ""))
                .map(objectName -> objectName.split("/")[0]).collect(Collectors.toList());
        return ResultUtil.success(snapshots);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse rollbackSnapshot(String projectId, String versionId, String snapshot) {
        String snapshotConfigFileName = buildSnapshotConfigFileName(projectId, versionId, snapshot);
        VersionSnapshotBO snapshotBO = minioService.funcInputStream(ProjectConstants.PROJECT_BUCKET_NAME, snapshotConfigFileName, inputStream -> VersionSnapshotBO.from(IOUtils.toString(inputStream)));
        if (!rollbackVersion(snapshotBO)){
            return ResultUtil.error("回滚版本失败");
        }

        if (!rollbackDependencies(snapshotBO.getDependenciesSnapshot())){
            return ResultUtil.error("回滚依赖失败");
        }

        if (!rollbackCode(projectId, versionId, snapshot)){
            return ResultUtil.error("回滚代码失败");
        }

        return ResultUtil.success();
    }

    private boolean rollbackCode(String projectId, String versionId, String snapshot) {
        minioService.copyPath(ProjectConstants.PROJECT_BUCKET_NAME, buildSnapshotCodePath(projectId, versionId, snapshot), buildProjectCodePath(projectId, versionId));
        return true;
    }

    private boolean rollbackDependencies(List<DependencySnapshotBO> dependenciesSnapshot) {
        dependenciesSnapshot.forEach(dependencySnapshotBO -> projectDependencyMapper.updateById(dependencySnapshotBO.to()));
        return true;
    }

    private boolean rollbackVersion(VersionSnapshotBO snapshotBO) {
        projectVersionMapper.updateById(snapshotBO.to());
        return true;
    }
}
