package com.sac.project.modules.fegin;


import com.sac.project.fegin.response.ResponseParameterInfo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

@FeignClient(value = "sac-thunderdb-provider")
public interface ThunderService {

    /**
     * 获取所有的点源
     *
     * @return
     */
    @RequestMapping(value = "/source/query", method = RequestMethod.GET)
    ResponseParameterInfo getSource();

}
