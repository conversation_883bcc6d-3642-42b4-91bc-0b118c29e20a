package com.sac.project.modules.idempotent.service.impl;

import com.sac.project.configure.redis.RedisUtil;
import com.sac.project.modules.idempotent.service.IdempotentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.UUID;

@Service
public class IdempotentServiceImpl implements IdempotentService {

    @Autowired
    private RedisUtil redisUtil;

    @Override
    public String createToken() {
        //生成uuid当作token
        String token = UUID.randomUUID().toString().replaceAll("-","");
        //将生成的token存入redis中
        redisUtil.set(token,token,180);
        //返回正确的结果信息
        return token;
    }
}
