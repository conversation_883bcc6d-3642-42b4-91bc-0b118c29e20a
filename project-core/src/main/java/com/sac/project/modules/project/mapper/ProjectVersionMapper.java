package com.sac.project.modules.project.mapper;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sac.framework.utils.UUIDGeneratorUtils;
import com.sac.project.configure.datasource.aspect.DataSource;
import com.sac.project.constant.Constant;
import com.sac.project.project.dto.NewVersionDTO;
import com.sac.project.project.dto.ProjectInfoQueryDTO;
import com.sac.project.project.entity.ProjectInfo;
import com.sac.project.project.entity.ProjectVersion;
import com.sac.project.project.vo.ProjectVersionVO;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Mapper
@DataSource("primary")
public interface ProjectVersionMapper extends BaseMapper<ProjectVersion> {
    default void newVersion(String projectId, NewVersionDTO newVersionDTO){
        Integer maxVersion = findMaxVersion(projectId);
        createVersionBy(projectId, newVersionDTO, maxVersion == null ? 1 : maxVersion + 1);
    }

    default void createVersionBy(String projectId, NewVersionDTO newVersionDTO, int version) {
        insert(newVersionDTO.to(projectId, version));
    }

    default Integer findMaxVersion(String projectId) {
        QueryWrapper<ProjectVersion> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("max(version) as maxVersion");
        queryWrapper.lambda().eq(ProjectVersion::getProjectId, projectId);
        Map<String, Object> stringObjectMap = selectMaps(queryWrapper).get(0);
        return (Integer)stringObjectMap.get("maxVersion");
    }

    default boolean isVersionExist(String versionId){
        return selectById(versionId) != null;
    }

    default List<ProjectVersionVO> queryVersionsByProjectId(String projectId){
        QueryWrapper<ProjectVersion> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ProjectVersion::getProjectId, projectId);
        List<ProjectVersion> projectVersions = selectList(queryWrapper);
        return projectVersions.stream().map(ProjectVersionVO::from).collect(Collectors.toList());
    }

    default ProjectVersion selectByProjectIdAndVersion(String projectId, String version) {
        QueryWrapper<ProjectVersion> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ProjectVersion::getProjectId, projectId)
                .eq(ProjectVersion::getVersion, version)
                .eq(ProjectVersion::getIsDeleted, Constant.NOT_DELETE_STR);
        return selectOne(queryWrapper);
    }

    default void batchDelete(List<String> idList) {
        UpdateWrapper<ProjectVersion> wrapper = new UpdateWrapper<>();
        wrapper.lambda()
                .in(ProjectVersion::getProjectId, idList)
                .eq(ProjectVersion::getIsDeleted, Constant.NOT_DELETE_STR);
        ProjectVersion projectVersion = ProjectVersion.builder().isDeleted(Constant.DELETE_STR).build();
        update(projectVersion, wrapper);
    }

    default void deleteByProjectId(String projectId){
        UpdateWrapper<ProjectVersion> wrapper = new UpdateWrapper<>();
        wrapper.lambda()
                .eq(ProjectVersion::getProjectId, projectId)
                .eq(ProjectVersion::getIsDeleted, Constant.NOT_DELETE_STR);
        ProjectVersion projectVersion = ProjectVersion.builder().isDeleted(Constant.DELETE_STR).build();
        update(projectVersion, wrapper);
    }

    default void updateByIdAndVersion(ProjectVersion vo) {
        ProjectVersion projectVersion = new ProjectVersion();
        projectVersion.setExtraInfo(vo.getExtraInfo());
        projectVersion.setVersion(vo.getVersion());
        UpdateWrapper<ProjectVersion> wrapper = new UpdateWrapper<>();
        wrapper.lambda()
                .eq(ProjectVersion::getProjectId, vo.getProjectId())
                .eq(ProjectVersion::getVersion, vo.getVersion())
                .eq(ProjectVersion::getIsDeleted, Constant.NOT_DELETE_STR);
        update(projectVersion, wrapper);
    }
}
