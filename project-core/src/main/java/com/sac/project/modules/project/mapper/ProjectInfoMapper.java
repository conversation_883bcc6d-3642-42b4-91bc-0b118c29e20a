package com.sac.project.modules.project.mapper;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sac.project.configure.datasource.aspect.DataSource;
import com.sac.project.constant.Constant;
import com.sac.project.project.dto.ProjectInfoQueryDTO;
import com.sac.project.project.entity.ProjectInfo;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
@DataSource("primary")
public interface ProjectInfoMapper extends BaseMapper<ProjectInfo> {
    default void batchDelete(List<String> idList) {
        UpdateWrapper<ProjectInfo> wrapper = new UpdateWrapper<>();
        wrapper.lambda()
                .in(ProjectInfo::getId, idList)
                .eq(ProjectInfo::getIsDeleted, Constant.NOT_DELETE_STR);
        ProjectInfo planInfo = ProjectInfo.builder().isDeleted(Constant.DELETE_STR).build();
        update(planInfo, wrapper);
    }

    default IPage<ProjectInfo> pageQuery(ProjectInfoQueryDTO dto) {
        Page<ProjectInfo> pageInfo = new Page<>(dto.getPageNo(), dto.getLimit());
        QueryWrapper<ProjectInfo> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .eq(StringUtils.isNotEmpty(dto.getName()), ProjectInfo::getName, dto.getName())
                .like(StringUtils.isNotEmpty(dto.getCategory()), ProjectInfo::getCategory, dto.getCategory())
                .eq(ProjectInfo::getIsDeleted, Constant.NOT_DELETE_STR);
        return selectPage(pageInfo, wrapper);
    }

    default boolean isCurrentVersion(String projectId, String versionId){
        QueryWrapper<ProjectInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(ProjectInfo::getId, projectId)
                .eq(ProjectInfo::getVersionId, versionId);
        return selectOne(queryWrapper) != null;
    }

    default void switchVersion(String projectId, String versionId){
        UpdateWrapper<ProjectInfo> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().set(ProjectInfo::getVersionId, versionId);
        updateWrapper.lambda().eq(ProjectInfo::getId, projectId);
        update(new ProjectInfo(), updateWrapper);
    }
}
