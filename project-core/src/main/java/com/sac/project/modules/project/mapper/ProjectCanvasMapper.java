package com.sac.project.modules.project.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sac.project.configure.datasource.aspect.DataSource;
import com.sac.project.project.entity.ProjectCanvas;
import com.sac.project.project.vo.ProjectCanvasVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * ProjectCanvasMapper
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2025/8/27 14:31
 */

@Mapper
@DataSource("primary")
public interface ProjectCanvasMapper extends BaseMapper<ProjectCanvas> {
    default List<ProjectCanvas> queryCanvasByProjectId(String projectId,String version) {
        LambdaQueryWrapper<ProjectCanvas> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectCanvas::getProjectId, projectId).eq(ProjectCanvas::getVersion, version);
        return selectList(queryWrapper);
    }

    default List<ProjectCanvas> queryByOperatorId(String operatorId) {
        LambdaQueryWrapper<ProjectCanvas> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectCanvas::getOperatorId, operatorId);
        return selectList(queryWrapper);
    }
}