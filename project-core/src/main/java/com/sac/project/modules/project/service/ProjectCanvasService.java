package com.sac.project.modules.project.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sac.project.project.dto.OperatorSyncDTO;
import com.sac.project.project.dto.ProjectCanvasSaveDTO;
import com.sac.project.project.entity.ProjectCanvas;
import com.sac.project.project.vo.ProjectCanvasVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * ProjectCanvasService
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2025/8/27 11:47
 */
public interface ProjectCanvasService extends IService<ProjectCanvas> {
    String importFile(String projectId, String version, String componentId, MultipartFile file);

    void saveCanvasInfo(String projectId, List<ProjectCanvasSaveDTO> saveDTOList, String version);

    List<ProjectCanvasVO> queryCanvas(String projectId, String version);

    void syncOperator(OperatorSyncDTO dto);

    void saveCanvasComponent(String id, String version, String componentId, ProjectCanvasSaveDTO dto);

    String createBlankPythonFile(String projectId, String version, String componentId);
}
