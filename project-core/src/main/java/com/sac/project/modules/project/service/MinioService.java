package com.sac.project.modules.project.service;

import com.sac.framework.constant.ThrowableFunction;
import com.sac.project.project.bo.VersionSnapshotBO;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.List;
import java.util.function.Function;

public interface MinioService {
    void upload(String bucketName, String objectName, String objectContent);

    void upload(String bucketName, String objectName, MultipartFile file);

    void copyPath(String bucketName, String resourcePath, String destPath);

    void batchDelete(String bucketName, List<String> filterList);

    void createBlankPythonFile(String bucketName, String filePath);

    List<String> listObjectName(String bucketName, String prefix);

    <R> R funcInputStream(String bucketName, String objectName, ThrowableFunction<InputStream, R> function);
}
