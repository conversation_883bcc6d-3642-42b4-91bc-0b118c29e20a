package com.sac.project.modules.project.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sac.framework.utils.DataUtils;
import com.sac.framework.utils.MinioUtil;
import com.sac.framework.utils.UUIDGeneratorUtils;
import com.sac.framework.utils.ZipUtils;
import com.sac.project.constant.Constant;
import com.sac.project.modules.base.service.impl.BaseServiceImpl;
import com.sac.project.modules.project.mapper.ProjectInfoMapper;
import com.sac.project.modules.project.mapper.ProjectVersionMapper;
import com.sac.project.modules.project.service.ProjectInfoService;
import com.sac.project.project.dto.ProjectInfoCopyDTO;
import com.sac.project.project.dto.ProjectInfoQueryDTO;
import com.sac.project.project.dto.ProjectInfoSaveDTO;
import com.sac.project.project.entity.ProjectInfo;
import com.sac.project.project.entity.ProjectVersion;
import com.sac.project.project.vo.ProjectDetailVO;
import com.sac.project.project.vo.ProjectInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ProjectInfoServiceImpl extends BaseServiceImpl<ProjectInfoMapper, ProjectInfo> implements ProjectInfoService {
    @Autowired
    private MinioUtil minioUtil;

    @Autowired
    private ProjectInfoMapper projectInfoMapper;

    @Autowired
    private ProjectVersionMapper projectVersionMapper;

    @Value("${minio.bucket-name}")
    private String bucketName;

    private static final String BASE_PATH = "project/";

    @Override
    public IPage<ProjectInfoVO> queryPage(ProjectInfoQueryDTO queryDTO) {
        IPage<ProjectInfo> iPage = projectInfoMapper.pageQuery(queryDTO);
        if (org.apache.commons.collections.CollectionUtils.isEmpty(iPage.getRecords())) {
            return new Page<>();
        }
        List<ProjectInfoVO> projectInfoVOList = iPage.getRecords().stream()
                .map(x -> {
                    ProjectInfoVO vo = new ProjectInfoVO();
                    BeanUtils.copyProperties(x, vo);
                    vo.setVersion(x.getVersionId());
                    return vo;
                }).collect(Collectors.toList());
        IPage<ProjectInfoVO> targetPage = new Page<>();
        targetPage.setRecords(projectInfoVOList);
        targetPage.setCurrent(iPage.getCurrent());
        targetPage.setSize(iPage.getSize());
        targetPage.setTotal(iPage.getTotal());
        targetPage.setPages(iPage.getPages());
        return targetPage;
    }

    @Override
    public ProjectDetailVO getDetail(String id) {
        ProjectInfo project = selectById(id);
        if (ObjectUtils.isEmpty(project)) {
            throw new RuntimeException("工程不存在或已删除");
        }
        ProjectDetailVO vo = new ProjectDetailVO();
        BeanUtils.copyProperties(project, vo);
        vo.setVersion(project.getVersionId());
        if (project.getVersionId() != null) {
            ProjectVersion version = projectVersionMapper.selectByProjectIdAndVersion(project.getId(),
                    project.getVersionId()
            );
            if (version != null) {
                vo.setExtraInfo(version.getExtraInfo());
                vo.setDevelopmentMode(version.getDevelopmentMode());
            }
        }
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String saveProject(ProjectInfoSaveDTO saveDTO) {
        ProjectInfo project = new ProjectInfo();
        BeanUtils.copyProperties(saveDTO, project);
        ProjectVersion projectVersion = new ProjectVersion();
        BeanUtils.copyProperties(saveDTO, projectVersion);
        if (saveDTO.getId() == null) {
            project.setId(UUIDGeneratorUtils.generateUUIDWithoutHyphens());
            project.setVersionId("1");
            projectVersion.setId(UUIDGeneratorUtils.generateUUIDWithoutHyphens());
            projectVersion.setVersion(1);
            projectVersion.setProjectId(project.getId());
            projectInfoMapper.insert(project);
            projectVersionMapper.insert(projectVersion);
        } else {
            projectVersion.setProjectId(project.getId());
            int version = DataUtils.removeVPrefixAndConvertToInt(saveDTO.getVersion());
            projectVersion.setVersion(version);
            projectVersion.setExtraInfo(saveDTO.getExtraInfo());
            projectInfoMapper.updateById(project);
            projectVersionMapper.updateByIdAndVersion(projectVersion);
        }
        return project.getId();
    }

    @Override
    public String copyProject(ProjectInfoCopyDTO copyDTO) {
        ProjectInfo project = selectById(copyDTO.getId());
        //TODO 依赖、版本信息及作业实例等

        project.setName(copyDTO.getName());
        project.setId(UUIDGeneratorUtils.generateUUIDWithoutHyphens());
        project.setVersionId("1");
        baseMapper.insert(project);


        //TODO  其他关联属性依赖、版本信息及作业实例等保存


        return project.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDelete(String ids) {
        if (StringUtils.isEmpty(ids)) {
            log.info("batchDelete ids is empty");
            return;
        }
        log.info("batchDelete ids : {}", ids);
        List<String> idList = DataUtils.strConvertList(ids);
        projectInfoMapper.batchDelete(idList);
        //todo 版本删除修改
//        projectVersionMapper.deleteBatchIds(ids);
    }

    @Override
    public byte[] exportProject(String projectId, String version) throws Exception {
        if (projectId == null || version == null) {
            throw new IllegalArgumentException("项目ID和版本号不能为空");
        }
        String basePath = BASE_PATH + projectId + "/" + version + "/";
        if (!minioUtil.pathExists(bucketName, basePath)) {
            throw new IllegalArgumentException("指定的版本不存在: " + version);
        }
        List<String> timestampDirs = minioUtil.listSubDirectories(bucketName, basePath);
        if (timestampDirs.isEmpty()) {
            throw new IllegalArgumentException("该版本下没有找到任何时间戳目录");
        }
        List<ZipUtils.ZipEntryInfo> zipEntries = new ArrayList<>();
        for (String timestampDir : timestampDirs) {
            String timestamp = extractTimestamp(timestampDir, basePath);
            if (timestamp == null) {
                continue;
            }
            String jsonFilePath = timestampDir + "project.json";
            if (minioUtil.pathExists(bucketName, jsonFilePath)) {
                try (InputStream is = minioUtil.getObject(bucketName, jsonFilePath)) {
                    byte[] content = minioUtil.inputStreamToByteArray(is);
                    zipEntries.add(new ZipUtils.ZipEntryInfo(timestamp + "/project.json", content));
                }
            }
            String codeDir = timestampDir + "code/";
            List<String> codeFiles = minioUtil.listObjects(bucketName, codeDir);
            for (String codeFile : codeFiles) {
                if (!codeFile.endsWith("/")) {
                    try (InputStream is = minioUtil.getObject(bucketName, codeFile)) {
                        byte[] content = minioUtil.inputStreamToByteArray(is);
                        String entryName = timestamp + "/code/" + codeFile.substring(codeDir.length());
                        zipEntries.add(new ZipUtils.ZipEntryInfo(entryName, content));
                    }
                }
            }
        }
        if (zipEntries.isEmpty()) {
            throw new RuntimeException("没有找到可导出的文件");
        }
        return ZipUtils.createZip(zipEntries);
    }

    @Override
    public List<String> findAllDistinctCategories() {
        QueryWrapper<ProjectInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("distinct CATEGORY");
        queryWrapper.lambda()
                .isNotNull(ProjectInfo::getCategory)
                .ne(ProjectInfo::getCategory, "");
        List<Object> categoryObjs = projectInfoMapper.selectObjs(queryWrapper);
        return categoryObjs.stream()
                .map(obj -> (String) obj)
                .collect(Collectors.toList());
    }

    /**
     * 从目录路径中提取时间戳
     */
    private String extractTimestamp(String dirPath, String basePath) {
        if (dirPath.startsWith(basePath) && dirPath.length() > basePath.length()) {
            String relativePath = dirPath.substring(basePath.length());
            if (relativePath.endsWith("/")) {
                relativePath = relativePath.substring(0, relativePath.length() - 1);
            }
            return relativePath;
        }
        return null;
    }

    @Override
    public void importProject(String projectId, String version, MultipartFile zipFile) throws Exception {
        if (zipFile == null || zipFile.isEmpty()) {
            throw new IllegalArgumentException("上传的文件不能为空");
        }
        String fileName = zipFile.getOriginalFilename();
        if (fileName == null || !fileName.toLowerCase().endsWith(".zip")) {
            throw new IllegalArgumentException("请上传ZIP格式的文件");
        }
        List<ZipUtils.ZipEntryInfo> entries = ZipUtils.parseZip(zipFile.getInputStream());
        if (entries.isEmpty()) {
            throw new IllegalArgumentException("ZIP文件中没有找到任何内容");
        }
        validateZipStructure(entries);
        String projectPath = BASE_PATH + projectId + "/";
        if (!minioUtil.pathExists(bucketName, projectPath)) {
            throw new IllegalArgumentException("MinIO中不存在对应工程的目录");
        }
        String versionPath = projectPath + version + "/";
        if (!minioUtil.pathExists(bucketName, versionPath)) {
            minioUtil.createDirectory(bucketName, versionPath);
        }
        for (ZipUtils.ZipEntryInfo entry : entries) {
            String entryName = entry.getEntryName();
            byte[] content = entry.getContent();
            String minioPath = versionPath + entryName;
            try (ByteArrayInputStream bais = new ByteArrayInputStream(content)) {
                minioUtil.putObject(bucketName, minioPath, bais, content.length, getContentType(entryName));
            }
        }
    }

    /**
     * 删除接口
     *
     * @param id id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        ProjectInfo projectInfo = new ProjectInfo();
        projectInfo.setId(id);
        projectInfo.setIsDeleted(Constant.DELETE_STR);
        modifyById(projectInfo);
        projectVersionMapper.deleteByProjectId(projectInfo.getId());
    }

    /**
     * 根据文件名获取内容类型
     */
    private String getContentType(String fileName) {
        if (fileName.endsWith(".json")) {
            return "application/json";
        } else if (fileName.endsWith(".py")) {
            return "text/x-python";
        }
        return "application/octet-stream";
    }

    private ProjectDetailVO convertToDetailVO(ProjectInfo project) {
        ProjectDetailVO vo = new ProjectDetailVO();
        BeanUtils.copyProperties(project, vo);
        return vo;
    }

    private void validateZipStructure(List<ZipUtils.ZipEntryInfo> entries) {
        Set<String> topLevelDirs = new HashSet<>();
        for (ZipUtils.ZipEntryInfo entry : entries) {
            String entryName = entry.getEntryName();
            if (entryName == null || entryName.trim().isEmpty()) {
                continue;
            }
            String[] pathSegments = entryName.split("/");
            if (pathSegments.length > 0 && !pathSegments[0].isEmpty()) {
                topLevelDirs.add(pathSegments[0]);
            }
        }
        if (topLevelDirs.size() != 1) {
            throw new IllegalArgumentException("ZIP包必须包含唯一的顶级目录");
        }
        String firstDir = topLevelDirs.iterator().next();
        try {
            Long.parseLong(firstDir);
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("ZIP包的顶级目录必须是时间戳格式");
        }
        boolean hasCodeDir = entries.stream()
                .map(ZipUtils.ZipEntryInfo::getEntryName)
                .anyMatch(entryName -> entryName.startsWith(firstDir + "/code/")
                        || entryName.equals(firstDir + "/code")
                        || entryName.equals(firstDir + "/code/"));
        if (!hasCodeDir) {
            throw new IllegalArgumentException("ZIP包的顶级目录下必须包含名为'code'的二级目录");
        }
    }

}
