package com.sac.project.modules.project.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sac.project.project.dto.ProjectInfoCopyDTO;
import com.sac.project.project.dto.ProjectInfoQueryDTO;
import com.sac.project.project.dto.ProjectInfoSaveDTO;
import com.sac.project.project.vo.ProjectDetailVO;
import com.sac.project.project.vo.ProjectInfoVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface ProjectInfoService {
    IPage<ProjectInfoVO> queryPage(ProjectInfoQueryDTO queryDTO);

    ProjectDetailVO getDetail(String id);

    String saveProject(ProjectInfoSaveDTO saveDTO);

    String copyProject(ProjectInfoCopyDTO copyDTO);

    void batchDelete(String ids);

    byte[] exportProject(String projectId, String version) throws Exception;

    void importProject(String projectId, String version, MultipartFile zipFile) throws Exception;

    void delete(String id);

    List<String> findAllDistinctCategories();
}
