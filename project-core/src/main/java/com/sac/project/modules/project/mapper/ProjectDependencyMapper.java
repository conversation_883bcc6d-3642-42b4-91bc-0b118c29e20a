package com.sac.project.modules.project.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sac.project.configure.datasource.aspect.DataSource;
import com.sac.project.project.entity.ProjectDependency;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * ProjectDependencyMapper
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2025/8/21 19:30
 */
@Mapper
@DataSource("primary")
public interface ProjectDependencyMapper extends BaseMapper<ProjectDependency> {
    default List<ProjectDependency> query(String id) {
        return selectList(new QueryWrapper<ProjectDependency>().lambda().in(ProjectDependency::getProjectId, id));
    }

    default List<ProjectDependency> findVersionDependencies(String versionId){
        return selectList(new QueryWrapper<ProjectDependency>().lambda().eq(ProjectDependency::getVersionId, versionId));
    }
}
