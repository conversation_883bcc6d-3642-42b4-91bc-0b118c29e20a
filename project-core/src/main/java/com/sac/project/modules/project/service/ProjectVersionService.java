package com.sac.project.modules.project.service;

import com.sac.framework.response.BaseResponse;
import com.sac.project.project.dto.NewVersionDTO;
import com.sac.project.project.vo.ProjectSnapshotVO;
import com.sac.project.project.vo.ProjectVersionVO;

import java.util.List;

public interface ProjectVersionService {

    BaseResponse newVersion(String projectId, NewVersionDTO newVersionDTO);

    BaseResponse delVersion(String projectId, String versionId);

    BaseResponse switchVersion(String projectId, String versionId);

    BaseResponse<List<ProjectVersionVO>> queryVersions(String projectId);

    BaseResponse newSnapshot(String projectId, String versionId);

    BaseResponse<List<String>> querySnapshots(String projectId, String versionId);

    BaseResponse rollbackSnapshot(String projectId, String versionId, String snapshot);
}
