package com.sac.project.modules.mybatis;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * MyMetaObjectHandler
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2025/8/5 15:16
 */

@Component
public class MyMetaObjectHandler implements MetaObjectHandler {

    // 插入时填充默认值
    @Override
    public void insertFill(MetaObject metaObject) {
        this.strictInsertFill(metaObject, "createTime", Date.class, new Date());
        this.strictInsertFill(metaObject, "updateTime", Date.class, new Date());
        this.strictInsertFill(metaObject, "creator", String.class, "system");
        this.strictInsertFill(metaObject, "updater", String.class, "system");
        this.strictInsertFill(metaObject, "isDeleted", String.class, "0");

        // String defaultCreator = SecurityUtils.getCurrentUserId(); // 获取当前登录用户
        // this.strictInsertFill(metaObject, "creator", String.class, defaultCreator);
    }

    // 更新时填充
    @Override
    public void updateFill(MetaObject metaObject) {
        this.strictUpdateFill(metaObject, "updateTime", Date.class, new Date());
        this.strictInsertFill(metaObject, "updater", String.class, "system");
        this.strictInsertFill(metaObject, "isDeleted", String.class, "0");
    }
}
