package com.sac.project.modules.project.service.impl;

import com.sac.framework.response.BaseResponse;
import com.sac.project.client.OperatorClient;
import com.sac.project.constant.Constant;
import com.sac.project.modules.project.service.OperatorGroupService;
import com.sac.project.project.vo.OperatorTreeVO;
import com.sac.project.project.vo.OperatorVO;
import com.sac.project.project.vo.TreeStructOperatorVO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * OperatorGroupServiceImpl
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2025/8/26 14:18
 */

@Service
public class OperatorGroupServiceImpl implements OperatorGroupService {
    private final Logger logger = LoggerFactory.getLogger(OperatorGroupServiceImpl.class);

    private static final String LEFT_PRE_STR = "l-";
    private static final String RIGHT_PRE_STR = "r-";
    private static final String USER_GROUP = "1";
    private static final String USER_PRE_STR = "7";
    private static final String SYSTEM_GROUP = "0";
    private static final String SYSTEM_PRE_STR = "6";
    private static final String USER_OPERATOR = "用户上传算子";
    private static final String SYS_OPERATOR = "系统算子";

    @Autowired
    private OperatorClient operatorClient;

    /**
     * 生成算子组
     *
     * @return 结果
     */
    @Override
    public List<OperatorTreeVO> getOperatorGroup() {
        BaseResponse<List<TreeStructOperatorVO>> result = operatorClient.queryOperators();
        // todo 调用模型服务查询组件
        if (ObjectUtils.isEmpty(result) || result.isFailed() || CollectionUtils.isEmpty(result.getData())) {
            logger.info("getOperatorGroup saveDTOList is empty");
            return Collections.emptyList();
        }
        List<TreeStructOperatorVO> data = result.getData();
        Map<String, List<TreeStructOperatorVO>> groupedByType = data.stream()
                .filter(vo -> StringUtils.isNotEmpty(vo.getType()))
                .collect(Collectors.groupingBy(TreeStructOperatorVO::getType));
        OperatorTreeVO userNode = createGroupNode(
                USER_PRE_STR,
                USER_OPERATOR,
                groupedByType.getOrDefault(USER_GROUP, Collections.emptyList()),
                USER_PRE_STR
        );
        OperatorTreeVO systemNode = createGroupNode(
                SYSTEM_PRE_STR,
                SYS_OPERATOR,
                groupedByType.getOrDefault(SYSTEM_GROUP, Collections.emptyList()),
                SYSTEM_PRE_STR
        );
        List<OperatorTreeVO> fixedStructure = getFixedStructure();
        fixedStructure.addAll(Arrays.asList(userNode, systemNode));
        return fixedStructure;
    }

    private OperatorTreeVO createGroupNode(String nodeId, String nodeName,
                                           List<TreeStructOperatorVO> childrenData, String preStr) {
        OperatorTreeVO node = new OperatorTreeVO();
        node.setId(nodeId);
        node.setName(nodeName);
        node.setChildren(convertTreeStruct(Constant.ROOT, childrenData, preStr));
        return node;
    }

    private List<OperatorTreeVO> convertTreeStruct(String parentId, List<TreeStructOperatorVO> list, String preStr) {
        final int[] index = {1};
        return list.stream()
                .filter(root -> root.getParentId().equals(parentId))
                .map(item -> {
                    logger.info("getOperatorGroup convertTreeStruct id:{}, name:{}", item.getId(), item.getName());
                    String currentId = buildId(preStr, String.valueOf(index[0]));
                    index[0] = index[0] + 1;
                    OperatorTreeVO.NodeModelsVO nodeModels = createNodeModels(false, false, null, null);
                    List<OperatorTreeVO> children = CollectionUtils.isNotEmpty(item.getList())
                            ? assembleOperator(currentId, item.getList())
                            : convertTreeStruct(item.getId(), list, currentId);
                    return createOperatorTreeVO(currentId, preStr, item.getName(), false, null, nodeModels, children);
                })
                .collect(Collectors.toList());
    }

    private List<OperatorTreeVO> assembleOperator(String parentId, List<OperatorVO> operatorList) {
        final int[] index = {1};
        return operatorList.stream()
                .map(item -> {
                    logger.info("getOperatorGroup assembleOperator id:{}, name:{}", item.getId(), item.getName());
                    String currentId = buildId(parentId, String.valueOf(index[0]));
                    index[0] = index[0] + 1;
                    OperatorTreeVO.NodeModelsVO nodeModels = createNodeModels(true, true,
                            LEFT_PRE_STR + currentId, RIGHT_PRE_STR + currentId);
                    return createOperatorTreeVO(currentId, parentId, item.getName(), true,
                            item.getId(), nodeModels, Collections.emptyList());
                })
                .collect(Collectors.toList());
    }

    private String buildId(String prefix, String id) {
        return prefix + Constant.SEPARATOR + id;
    }

    private OperatorTreeVO.NodeModelsVO createNodeModels(boolean hasLeftPorts, boolean hasRightPorts,
                                                         String leftPortId, String rightPortId) {
        return new OperatorTreeVO.NodeModelsVO().toBuilder()
                .hasLeftPorts(hasLeftPorts)
                .hasRightPorts(hasRightPorts)
                .leftPortId(leftPortId)
                .rightPortId(rightPortId)
                .build();
    }

    private OperatorTreeVO createOperatorTreeVO(String id, String parentId, String name, boolean isOperator,
                                                String operatorId, OperatorTreeVO.NodeModelsVO models,
                                                List<OperatorTreeVO> children) {
        return new OperatorTreeVO().toBuilder()
                .id(id)
                .parentId(parentId)
                .name(name)
                .isOperator(isOperator)
                .operatorId(operatorId)
                .models(models)
                .children(children)
                .build();
    }

    private List<OperatorTreeVO> getFixedStructure() {
        List<OperatorTreeVO> allNodeList = new ArrayList<>();
        allNodeList.add(createGroup(
                "1", "输入",
                Arrays.asList(
                        createOperator("1-1", "1", "输入", "r-1-1", "", true, false),
                        createOperator("1-2", "1", "设备输入", "r-1-2", "", true, false)
                )
        ));
        allNodeList.add(createGroup(
                "2", "输出",
                Collections.singletonList(
                        createOperator("2-1", "2", "输出", "", "l-2-1", false, true)
                )
        ));
        allNodeList.add(createGroup(
                "3", "python自定义",
                Collections.singletonList(
                        createOperator("3-1", "3", "python自定义", "r-3-1", "l-3-1", true, true)
                )
        ));
        allNodeList.add(createGroup(
                "4", "数据转换",
                Arrays.asList(
                        createOperator("4-1", "4", "数据输入转换", "r-4-1", "l-4-1", true, true),
                        createOperator("4-2", "4", "数据输出转换", "r-4-2", "l-4-2", true, true)
                )
        ));
        allNodeList.add(createGroup(
                "5", "在线调用",
                Collections.singletonList(
                        createOperator("5-1", "5", "在线调用算子", "r-5-1", "l-5-1", true, true)
                )
        ));
        return allNodeList;
    }

    private OperatorTreeVO createGroup(String groupId, String groupName, List<OperatorTreeVO> children) {
        OperatorTreeVO group = new OperatorTreeVO();
        group.setId(groupId);
        group.setName(groupName);
        group.setChildren(children);
        return group;
    }

    private OperatorTreeVO createOperator(String id, String parentId, String name, String rightPortId,
                                          String leftPortId, boolean hasRightPorts, boolean hasLeftPorts) {
        OperatorTreeVO.NodeModelsVO models = new OperatorTreeVO.NodeModelsVO(
                rightPortId, leftPortId, hasRightPorts, hasLeftPorts
        );
        OperatorTreeVO operator = new OperatorTreeVO();
        operator.setId(id);
        operator.setParentId(parentId);
        operator.setName(name);
        operator.setIsOperator(true);
        operator.setModels(models);
        operator.setChildren(new ArrayList<>());
        return operator;
    }
}
