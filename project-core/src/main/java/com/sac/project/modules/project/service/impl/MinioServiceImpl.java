package com.sac.project.modules.project.service.impl;

import com.sac.framework.constant.ThrowableConsumer;
import com.sac.framework.constant.ThrowableFunction;
import com.sac.project.modules.project.service.MinioService;
import io.minio.MinioClient;
import io.minio.PutObjectOptions;
import io.minio.Result;
import io.minio.errors.*;
import io.minio.messages.Item;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.function.Function;

@Service
public class MinioServiceImpl implements MinioService {
    private final Logger logger = LoggerFactory.getLogger(MinioServiceImpl.class);

    private static final String MINIO_UPLOAD_ERROR = "文件上传失败";
    private static final String MINIO_CREATE_ERROR = "创建空白文件失败";

    @Autowired
    MinioClient minioClient;

    @Override
    public void upload(String bucketName, String objectName, String objectContent) {
        byte[] bytes = objectContent.getBytes();
        try (ByteArrayInputStream inputStream = new ByteArrayInputStream(bytes)) {
            logger.info("Minio upload file:{}", objectName);
            createBucket(bucketName);
            PutObjectOptions options = new PutObjectOptions(bytes.length, -1);
            minioClient.putObject(bucketName, objectName, inputStream, options);
        } catch (Exception e) {
            logger.error("MinIO upload error:{}", e.getMessage());
            throw new RuntimeException(MINIO_UPLOAD_ERROR);
        }
    }

    @Override
    public void upload(String bucketName, String objectName, MultipartFile file) {
        try (InputStream inputStream = file.getInputStream()) {
            logger.info("Minio upload file:{}", objectName);
            createBucket(bucketName);
            PutObjectOptions options = new PutObjectOptions(file.getSize(), -1);
            options.setContentType(file.getContentType());
            minioClient.putObject(bucketName, objectName, inputStream, options);
        } catch (Exception e) {
            logger.error("MinIO upload error:{}", e.getMessage());
            throw new RuntimeException(MINIO_UPLOAD_ERROR);
        }
    }

    @Override
    public void createBlankPythonFile(String bucketName, String filePath) {
        byte[] emptyContent = new byte[0];
        try(ByteArrayInputStream inputStream = new ByteArrayInputStream(emptyContent);) {
            createBucket(bucketName);
            PutObjectOptions options = new PutObjectOptions(emptyContent.length, -1);
            minioClient.putObject(bucketName, filePath, inputStream, options);
        } catch (Exception e) {
            logger.error("Minio createBlankPythonFile upload error:{}", e.getMessage());
            throw new RuntimeException(MINIO_CREATE_ERROR);
        }
    }

    @Override
    public List<String> listObjectName(String bucketName, String prefix) {
        return itemsFunc(bucketName, prefix, Item::objectName);
    }

    @Override
    public <R> R funcInputStream(String bucketName, String objectName, ThrowableFunction<InputStream, R> function) {
        try {
            try(InputStream inputStream = minioClient.getObject(bucketName, objectName)) {
                return function.apply(inputStream);
            }
        } catch (Throwable e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 创建存储桶
     * @param bucketName 存储桶名称
     */
    private void createBucket(String bucketName) throws Exception {
        if (!minioClient.bucketExists(bucketName)) {
            minioClient.makeBucket(bucketName);
        }
    }

    @Override
    public void copyPath(String bucketName, String resourcePath, String destPath) {
        itemsConsumer(bucketName, resourcePath, item -> {
            String objectName = item.objectName();
            String newObjectName = objectName.replace(resourcePath, destPath);
            try(InputStream inputStream = minioClient.getObject(bucketName, objectName)) {
                minioClient.putObject(bucketName, newObjectName, inputStream, null);
            }
        });
    }

    private void itemsConsumer(String bucketName, String prefix, ThrowableConsumer<Item> consumer){
        itemsFunc(bucketName, prefix, item -> {consumer.accept(item);return null;});
    }

    private <T> List<T> itemsFunc(String bucketName, String prefix, ThrowableFunction<Item, T> func){
        try {
            return iterateItemResult(minioClient.listObjects(bucketName, prefix).iterator(), func, new ArrayList<>());
        } catch (Throwable e) {
            throw new RuntimeException(e);
        }
    }

    private <T> List<T> iterateItemResult(Iterator<Result<Item>> itemResults, ThrowableFunction<Item, T> func, List<T> list) throws Throwable {
        if (!itemResults.hasNext()){
            return list;
        }

        list.add(func.apply(itemResults.next().get()));
        return iterateItemResult(itemResults, func, list);
    }

    @Override
    public void batchDelete(String bucketName, List<String> filePathList) {
        if (CollectionUtils.isEmpty(filePathList)) {
            return;
        }
        logger.info("Minio batch delete:{}", filePathList);
        try {
            for (String filePath : filePathList) {
                minioClient.removeObject(bucketName,filePath);
            }
        } catch (Exception e) {
            logger.error("Minio batch delete error:{}", e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
    }
}
