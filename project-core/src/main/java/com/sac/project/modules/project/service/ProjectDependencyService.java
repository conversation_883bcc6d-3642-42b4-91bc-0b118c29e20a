package com.sac.project.modules.project.service;

import com.sac.project.project.dto.ProjectDependencySaveDTO;
import com.sac.project.project.vo.ProjectDependencyVO;

import java.util.List;

/**
 * ProjectDependencyService
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2025/8/21 19:20
 */
public interface ProjectDependencyService {
    List<ProjectDependencyVO> queryDependency(String id);

    void saveDependency(ProjectDependencySaveDTO dto);

    void deleteDependency(String ids, String projectId);
}
