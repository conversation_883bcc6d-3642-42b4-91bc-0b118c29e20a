package com.sac.project.modules.project.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sac.project.configure.redis.RedisUtil;
import com.sac.project.constant.Constant;
import com.sac.project.modules.project.mapper.ProjectCanvasMapper;
import com.sac.project.modules.project.service.MinioService;
import com.sac.project.modules.project.service.ProjectCanvasService;
import com.sac.project.project.dto.OperatorSyncDTO;
import com.sac.project.project.dto.ProjectCanvasSaveDTO;
import com.sac.project.project.entity.ProjectCanvas;
import com.sac.project.project.vo.ProjectCanvasVO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.stream.Collectors;

/**
 * ProjectCanvasServiceImpl
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2025/8/27 11:48
 */

@Service
public class ProjectCanvasServiceImpl extends ServiceImpl<ProjectCanvasMapper, ProjectCanvas> implements ProjectCanvasService {
    private final Logger logger = LoggerFactory.getLogger(ProjectCanvasServiceImpl.class);
    private static final long SEVEN_DAYS = 7 * 24 * 60 * 60;
    private static final String REDIS_KEY_STR = "%s_%s_%s";
    private static final String MINIO_FILE = "project/%s/%s/latest/code/%s/%s";
    private static final String MINIO_PATH = "project/%s/%s/latest/code/%s/";
    private static final String JUPYTER_LAB_URL = "%s:%s/%s";
    private static final String PYTHON_FILE_NAME = "blank_script.py";
    private static final String MINIO_PRE_STR = "project/";

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private MinioService minioService;

    @Autowired
    private ProjectCanvasMapper projectCanvasMapper;

    @Value("${jupyterLab.url}")
    private String jupyterLabUrl;

    /**
     * 文件上传
     *
     * @param projectId projectId
     * @param version version
     * @param componentId componentId
     * @param file file
     * @return 路径
     */
    @Override
    public String importFile(String projectId, String version, String componentId, MultipartFile file) {
        logger.info("importFile file name : {}", file.getOriginalFilename());
        String minioPath = String.format(MINIO_FILE, projectId, version, componentId, file.getOriginalFilename());
        minioService.upload(Constant.PROJECT_BUCKET_NAME, minioPath, file);
        redisUtil.lSet(String.format(REDIS_KEY_STR, projectId, version, componentId), minioPath, SEVEN_DAYS);
        return String.format(JUPYTER_LAB_URL, jupyterLabUrl, Constant.PROJECT_BUCKET_NAME, minioPath);
    }

    /**
     * 保存画布信息
     *
     * @param saveDTOList saveDTOList
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveCanvasInfo(String projectId, List<ProjectCanvasSaveDTO> saveDTOList, String version) {
        if (CollectionUtils.isEmpty(saveDTOList)) {
            logger.info("saveCanvasInfo saveDTOList is empty");
            return;
        }
        logger.info("saveCanvasInfo save size:{}", saveDTOList.size());
        List<ProjectCanvas> dbCanvasList = projectCanvasMapper.queryCanvasByProjectId(projectId, version);
        List<String> saveIdList = saveDTOList.stream()
                .map(ProjectCanvasSaveDTO::getId).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        List<String> dbIdList = dbCanvasList.stream()
                .map(ProjectCanvas::getId).collect(Collectors.toList());
        List<String> deleteIdList = dbIdList.stream()
                .filter(id -> !saveIdList.contains(id)).collect(Collectors.toList());
        List<ProjectCanvas> saveList = new ArrayList<>();
        List<ProjectCanvas> updateList = new ArrayList<>();
        for (ProjectCanvasSaveDTO dto : saveDTOList) {
            String dtoId = dto.getId();
            if (StringUtils.isEmpty(dtoId)) {
                continue;
            }
            dto.setVersion(version);
            dto.setProjectId(projectId);
            ProjectCanvas canvas = convertToEntity(dto);
            if (dbIdList.contains(dtoId)) {
                updateList.add(canvas);
            } else {
                canvas.setProjectId(projectId);
                saveList.add(canvas);
            }
        }
        if (CollectionUtils.isNotEmpty(saveList)) {
            logger.info("saveCanvasInfo saveList size:{}", saveList.size());
            saveBatch(saveList);
        }
        if (CollectionUtils.isNotEmpty(updateList)) {
            logger.info("saveCanvasInfo updateList size:{}", updateList.size());
            updateBatchById(updateList);
        }
        if (CollectionUtils.isNotEmpty(deleteIdList)) {
            logger.info("saveCanvasInfo deleteIdList size:{}", deleteIdList.size());
            projectCanvasMapper.deleteBatchIds(deleteIdList);
        }
    }

    private ProjectCanvas convertToEntity(ProjectCanvasSaveDTO dto) {
        ProjectCanvas canvas = new ProjectCanvas();
        BeanUtils.copyProperties(dto, canvas);
        if (CollectionUtils.isNotEmpty(dto.getMinioPath())) {
            canvas.setMinioPath(String.join(Constant.COMMA, dto.getMinioPath()));
        }
        return canvas;
    }

    /**
     * 查询画布节点
     *
     * @param projectId projectId
     * @return 结果
     */
    @Override
    public List<ProjectCanvasVO> queryCanvas(String projectId ,String version) {
        if (StringUtils.isEmpty(projectId)) {
            logger.info("queryCanvas projectId is empty.");
            return Collections.emptyList();
        }
        List<ProjectCanvas> projectCanvas = projectCanvasMapper.queryCanvasByProjectId(projectId, version);
        if (CollectionUtils.isEmpty(projectCanvas)) {
            logger.info("queryCanvas projectCanvas is empty.");
            return Collections.emptyList();
        }
        logger.info("queryCanvas projectCanvas size:{}", projectCanvas.size());
        return projectCanvas.stream()
                .map(item -> {
                    ProjectCanvasVO vo = new ProjectCanvasVO();
                    BeanUtils.copyProperties(item, vo);
                    return vo;
                })
                .collect(Collectors.toList());
    }

    /**
     * 同步算子文件
     *
     * @param dto dto
     */
    @Override
    public void syncOperator(OperatorSyncDTO dto) {
        if (ObjectUtils.isEmpty(dto)) {
            return;
        }
        List<ProjectCanvas> queryList = projectCanvasMapper.queryByOperatorId(dto.getOperatorId());
        if (CollectionUtils.isEmpty(queryList)) {
            logger.info("syncOperator queryList is empty");
            return;
        }
        // todo
        String targetPath = "";
        minioService.copyPath(dto.getBucketName(), dto.getMinioPath(), targetPath);
    }

    /**
     * 保存单个画布组件
     *
     * @param id id
     * @param version version
     * @param componentId componentId
     * @param dto dto
     */
    @Override
    public void saveCanvasComponent(String id, String version, String componentId, ProjectCanvasSaveDTO dto) {
        if (ObjectUtils.isEmpty(dto)) {
            logger.info("saveCanvasComponent dto is empty");
            return;
        }
        String redisKey = String.format(REDIS_KEY_STR, id, version, componentId);
        syncMinioFile(dto, redisKey);
        dto.setVersion(version);
        dto.setId(componentId);
        dto.setProjectId(id);
        ProjectCanvas projectCanvas = convertToEntity(dto);
        ProjectCanvas dbInfo = projectCanvasMapper.selectById(projectCanvas.getId());
        if (ObjectUtils.isEmpty(dbInfo)) {
            projectCanvasMapper.insert(projectCanvas);
        } else {
            projectCanvasMapper.updateById(projectCanvas);
        }
        //todo 如果是算子模块，需要把该算子的minio文件拷贝到工程的minio下
    }

    private void syncMinioFile(ProjectCanvasSaveDTO dto, String redisKey) {
        if (!redisUtil.hasKey(redisKey)) {
            return;
        }
        logger.info("saveCanvasComponent redisKey is exist {}", redisKey);
        List<String> minioPath = dto.getMinioPath().stream()
                .map(url -> {
                    int index = url.indexOf(MINIO_PRE_STR);
                    return index != -1 ? url.substring(index) : url;
                })
                .distinct()
                .collect(Collectors.toList());
        List<Object> cachePath = redisUtil.lGet(redisKey, 0, -1);
        List<String> deleteList = Optional.ofNullable(cachePath)
                .orElse(Collections.emptyList())
                .stream()
                .filter(Objects::nonNull)
                .filter(obj -> obj instanceof String)
                .map(obj -> (String) obj)
                .filter(str -> !minioPath.contains(str))
                .collect(Collectors.toList());
        logger.info("saveCanvasComponent deleteList size:{}", deleteList.size());
        minioService.batchDelete(Constant.PROJECT_BUCKET_NAME, deleteList);
        redisUtil.delete(redisKey);
    }

    /**
     * 创建空白python文件
     *
     * @param projectId projectId
     * @param version version
     * @param componentId componentId
     * @return 文件路径
     */
    @Override
    public String createBlankPythonFile(String projectId, String version, String componentId) {
        String minioPath = String.format(MINIO_PATH, projectId, version, componentId);
        minioService.createBlankPythonFile(Constant.PROJECT_BUCKET_NAME, minioPath + PYTHON_FILE_NAME);
        return String.format(JUPYTER_LAB_URL, jupyterLabUrl, Constant.PROJECT_BUCKET_NAME, minioPath);
    }
}