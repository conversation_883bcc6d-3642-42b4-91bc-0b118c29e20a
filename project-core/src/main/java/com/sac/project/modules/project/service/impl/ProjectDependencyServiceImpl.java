package com.sac.project.modules.project.service.impl;

import com.sac.framework.utils.DataUtils;
import com.sac.framework.utils.UUIDGeneratorUtils;
import com.sac.project.constant.Constant;
import com.sac.project.constant.VersionRelationEnum;
import com.sac.project.modules.project.mapper.ProjectDependencyMapper;
import com.sac.project.modules.project.service.ProjectDependencyService;
import com.sac.project.project.dto.ProjectDependencySaveDTO;
import com.sac.project.project.entity.ProjectDependency;
import com.sac.project.project.vo.ProjectDependencyVO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * ProjectDependencyServiceImpl
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2025/8/21 19:21
 */

@Service
public class ProjectDependencyServiceImpl implements ProjectDependencyService {
    private final Logger logger = LoggerFactory.getLogger(ProjectDependencyServiceImpl.class);
    private static final String DEPENDENCY_DELETE_ERROR = "ID: %s，不属于该工程";

    @Autowired
    private ProjectDependencyMapper projectDependencyMapper;

    /**
     * 查询依赖
     *
     * @param id id
     * @return 结果
     */
    @Override
    public List<ProjectDependencyVO> queryDependency(String id) {
        List<ProjectDependency> dependencyList = projectDependencyMapper.query(id);
        return dependencyList.stream()
                .map(vo -> {
                    ProjectDependencyVO projectDependencyVO = new ProjectDependencyVO();
                    BeanUtils.copyProperties(vo, projectDependencyVO);
                    projectDependencyVO.setVersionRelationSymbol(
                            VersionRelationEnum.getDescriptionByCode(vo.getVersionRelation()));
                    return projectDependencyVO;
                }).collect(Collectors.toList());
    }

    /**
     * 保存依赖
     *
     * @param dto
     */
    @Override
    public void saveDependency(ProjectDependencySaveDTO dto) {
        ProjectDependency projectDependency = new ProjectDependency();
        BeanUtils.copyProperties(dto, projectDependency);
        if (StringUtils.isEmpty(dto.getId())) {
            String id = UUIDGeneratorUtils.generateUUIDWithoutHyphens();
            projectDependency.setId(id);
            projectDependencyMapper.insert(projectDependency);
        } else {
            projectDependencyMapper.updateById(projectDependency);
        }
    }

    /**
     * 删除依赖
     *
     * @param ids ids
     * @param projectId projectId
     */
    @Override
    public void deleteDependency(String ids, String projectId) {
        logger.info("PROJECT dependency delete id : {}", ids);
        List<String> deleteIdList = DataUtils.strConvertList(ids);
        if (CollectionUtils.isEmpty(deleteIdList)) {
            logger.info("PROJECT dependency delete list is empty");
            return;
        }
        ProjectDependency projectDependency = new ProjectDependency();
        projectDependency.setProjectId(projectId);
        List<String> idList = projectDependencyMapper.query(projectId).stream()
                .map(ProjectDependency::getId)
                .collect(Collectors.toList());
        List<String> invaildIdList = deleteIdList.stream()
                .filter(deleteId -> !idList.contains(deleteId)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(invaildIdList)) {
            throw new RuntimeException(String.format(DEPENDENCY_DELETE_ERROR,
                    StringUtils.join(invaildIdList, Constant.COMMA)));
        }
        projectDependencyMapper.deleteBatchIds(deleteIdList);
    }
}
