package com.sac.project.constant;

import com.sac.project.project.vo.CodeItemVO;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * VersionRelationEnum
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2025/8/14 11:00
 */

@Getter
public enum VersionRelationEnum {

    /**
     * 等于
     */
    EQUALS("eq", "=="),

    /**
     * 大于等于
     */
    GREATER_THAN_OR_EQUAL("ge", ">="),

    /**
     * 小于等于
     */
    LESS_THAN_OR_EQUAL("le", "<=");

    private final String code;
    private final String name;

    /**
     * 构造方法
     * @param code 关系符号
     * @param name 关系名称
     */
    VersionRelationEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    // 缓存编码与枚举的映射关系
    private static final Map<String, VersionRelationEnum> CODE_MAP;

    static {
        CODE_MAP = new HashMap<>(values().length);
        for (VersionRelationEnum type : values()) {
            CODE_MAP.put(type.code, type);
        }
    }

    /**
     * 根据编码获取枚举实例
     */
    public static String getDescriptionByCode(String code) {
        return Optional.ofNullable(CODE_MAP.get(code))
                .map(VersionRelationEnum::getName)
                .orElse(StringUtils.EMPTY);
    }

    /**
     * 获取全部枚举的Map结构（key:编码，value:描述）
     *
     * @return 包含所有IO类型的Map
     */
    public static List<CodeItemVO> getAllEnumList() {
        return Stream.of(values())
                .map(v -> new CodeItemVO(v.code, v.name))
                .collect(Collectors.toList());
    }

}
