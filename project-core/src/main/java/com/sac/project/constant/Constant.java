package com.sac.project.constant;

/**
 * 常量
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2022/3/14
 */
public class Constant {

    /**
     * 已删除
     */
    public static Integer DELETE = 1;

    /**
     * 未删除
     */
    public static Integer NOT_DELETE = 0;

    /**
     * FDFS
     */
    public static String FDFS = "FDFS";

    /**
     * NAS
     */
    public static String NAS = "NAS";

    /**
     * 附件库
     */
    public static String ATTACHEMENT = "attachment";

    /**
     * pdf
     */
    public static String PDF = "pdf";

    /**
     * redis
     */
    public static String REDIS = "REDIS";

    /**
     * caffeine
     */
    public static String CAFFEINE = "CAFFEINE";

    /**
     * memcached
     */
    public static String MEMCACHED = "MEMCACHED";

    public static final String PROJECT_BUCKET_NAME = "mechanism";

    public static String DELETE_STR = "1";

    public static String NOT_DELETE_STR = "0";

    public static String COMMA = ",";
    public static String ROOT = "0";

    public static String SEPARATOR = "-";
}
