<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sac.project.modules.project.mapper.ProjectVersionMapper">

    <select id="selectByProjectIdAndVersion" resultType="com.sac.project.project.entity.ProjectVersion">
        SELECT
            ID,
            PROJECT_ID,
            VERSION,
            DEVELOPMENT_MODE,
            EXTRA_INFO
        FROM
            PROJECT_VERSION
        WHERE
            PROJECT_ID = #{projectId}
          AND VERSION = #{version}
          AND IS_DELETED = '0'
    </select>



</mapper>