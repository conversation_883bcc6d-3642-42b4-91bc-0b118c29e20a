<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sac.project.modules.project.mapper.ProjectInfoMapper">

    <select id="pageQueryWithVersion" resultType="com.sac.project.project.vo.ProjectInfoVO">
        SELECT
        pi.ID AS id,
        pi.NAME AS name,
        pi.VERSION AS version,
        pi.CREATE_TIME AS createTime,
        pi.CREATOR AS creator,
        pv.CATEGORY AS category,
        pv.EXTRA_INFO AS extraInfo
        FROM
        PROJECT_INFO pi
        LEFT JOIN
        PROJECT_VERSION pv
        ON pi.ID = pv.PROJECT_ID
        AND CAST(pv.VERSION AS VARCHAR) = pi.VERSION
        WHERE
        pi.IS_DELETED = #{notDeleteStr}
        <if test="dto.name != null and dto.name != ''">
            AND pi.NAME LIKE CONCAT('%', #{dto.name}, '%')
        </if>
        <if test="dto.category != null and dto.category != ''">
            AND pv.CATEGORY LIKE CONCAT('%', #{dto.category}, '%')
        </if>
        ORDER BY
        pi.CREATE_TIME DESC
    </select>
</mapper>