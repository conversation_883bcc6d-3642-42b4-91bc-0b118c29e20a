package com.sac.framework.request;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * 请求转换器工具类
 * <p>
 * 提供请求转换过程中的通用工具方法
 */
public class RequestConverterUtils {

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    private static final String PARAM_SEPARATOR = "^";
    
    /**
     * 将下划线命名法转换为小驼峰命名法
     * 例如：FIELD_NAME -> fieldName
     *
     * @param underscoreName 下划线命名的字符串
     * @return 小驼峰命名的字符串
     */
    public static String toCamelCase(String underscoreName) {
        if (underscoreName == null || underscoreName.isEmpty()) {
            return underscoreName;
        }
        
        // 先转为小写
        String lowercaseName = underscoreName.toLowerCase();
        
        StringBuilder result = new StringBuilder();
        boolean nextUpper = false;
        
        for (int i = 0; i < lowercaseName.length(); i++) {
            char c = lowercaseName.charAt(i);
            
            if (c == '_') {
                nextUpper = true;
            } else {
                if (nextUpper) {
                    result.append(Character.toUpperCase(c));
                    nextUpper = false;
                } else {
                    result.append(c);
                }
            }
        }
        
        return result.toString();
    }
    
    /**
     * 从参数名称中提取字段名，并转换为小驼峰命名法
     * 参数名称格式为：Q^字段名_^SL，例如 Q^FIELD_NAME_^SL
     * 
     * @param paramKey 参数名称
     * @return 提取的字段名（小驼峰格式）
     */
    public static String extractFieldName(String paramKey) {
        if (paramKey == null || paramKey.isEmpty()) {
            return "";
        }
        
        // 分割参数名称
        String[] parts = paramKey.split("\\^");
        if (parts.length < 3) {
            // 如果格式不匹配，返回原始参数名称
            return paramKey;
        }
        
        // 提取中间部分（字段名+下划线）
        String fieldNameWithUnderscore = parts[1];
        
        // 去掉末尾的下划线（如果有）
        if (fieldNameWithUnderscore.endsWith("_")) {
            fieldNameWithUnderscore = fieldNameWithUnderscore.substring(0, fieldNameWithUnderscore.length() - 1);
        }
        
        // 转换为小驼峰命名法
        return toCamelCase(fieldNameWithUnderscore);
    }

    /**
     * 从字段参数列表中提取指定字段的值
     *
     * @param parameters 字段参数列表
     * @param fieldName  字段名（小驼峰格式）
     * @return 字段值的Optional包装
     */
    public static Optional<String> getFieldValue(List<FieldParam> parameters, String fieldName) {
        if (parameters == null || parameters.isEmpty()) {
            return Optional.empty();
        }
        
        return parameters.stream()
                .filter(param -> {
                    String extractedFieldName = extractFieldName(param.getKey());
                    return fieldName.equals(extractedFieldName);
                })
                .map(FieldParam::getValue)
                .findFirst();
    }

    /**
     * 将字段参数列表转换为字段名到字段值的映射
     *
     * @param parameters 字段参数列表
     * @return 字段名（小驼峰格式）到字段值的映射
     */
    public static Map<String, String> toFieldMap(List<FieldParam> parameters) {
        if (parameters == null || parameters.isEmpty()) {
            return Collections.emptyMap();
        }
        
        Map<String, String> fieldMap = new HashMap<>();
        for (FieldParam param : parameters) {
            String fieldName = extractFieldName(param.getKey());
            fieldMap.put(fieldName, param.getValue());
        }
        return fieldMap;
    }

    /**
     * 处理字符串类型的字段值
     *
     * @param parameters   字段参数列表
     * @param fieldName    字段名（小驼峰格式）
     * @param targetSetter 目标对象的setter方法引用
     */
    public static void handleStringField(List<FieldParam> parameters, String fieldName, Consumer<String> targetSetter) {
        getFieldValue(parameters, fieldName)
                .ifPresent(targetSetter);
    }

    /**
     * 处理整数类型的字段值
     *
     * @param parameters   字段参数列表
     * @param fieldName    字段名（小驼峰格式）
     * @param targetSetter 目标对象的setter方法引用
     */
    public static void handleIntegerField(List<FieldParam> parameters, String fieldName, Consumer<Integer> targetSetter) {
        getFieldValue(parameters, fieldName)
                .map(Integer::valueOf)
                .ifPresent(targetSetter);
    }

    /**
     * 处理长整数类型的字段值
     *
     * @param parameters   字段参数列表
     * @param fieldName    字段名（小驼峰格式）
     * @param targetSetter 目标对象的setter方法引用
     */
    public static void handleLongField(List<FieldParam> parameters, String fieldName, Consumer<Long> targetSetter) {
        getFieldValue(parameters, fieldName)
                .map(Long::valueOf)
                .ifPresent(targetSetter);
    }

    /**
     * 处理浮点数类型的字段值
     *
     * @param parameters   字段参数列表
     * @param fieldName    字段名（小驼峰格式）
     * @param targetSetter 目标对象的setter方法引用
     */
    public static void handleDoubleField(List<FieldParam> parameters, String fieldName, Consumer<Double> targetSetter) {
        getFieldValue(parameters, fieldName)
                .map(Double::valueOf)
                .ifPresent(targetSetter);
    }

    /**
     * 处理日期类型的字段值
     *
     * @param parameters   字段参数列表
     * @param fieldName    字段名（小驼峰格式）
     * @param targetSetter 目标对象的setter方法引用
     */
    public static void handleDateField(List<FieldParam> parameters, String fieldName, Consumer<LocalDate> targetSetter) {
        getFieldValue(parameters, fieldName)
                .map(dateStr -> LocalDate.parse(dateStr, DATE_FORMATTER))
                .ifPresent(targetSetter);
    }

    /**
     * 处理日期时间类型的字段值
     *
     * @param parameters   字段参数列表
     * @param fieldName    字段名（小驼峰格式）
     * @param targetSetter 目标对象的setter方法引用
     */
    public static void handleDateTimeField(List<FieldParam> parameters, String fieldName, Consumer<LocalDateTime> targetSetter) {
        getFieldValue(parameters, fieldName)
                .map(dateTimeStr -> LocalDateTime.parse(dateTimeStr, DATETIME_FORMATTER))
                .ifPresent(targetSetter);
    }

    /**
     * 处理自定义类型转换的字段值
     *
     * @param parameters    字段参数列表
     * @param fieldName     字段名（小驼峰格式）
     * @param valueMapper   值转换函数
     * @param targetSetter  目标对象的setter方法引用
     */
    public static <T, R> void handleCustomField(List<FieldParam> parameters, String fieldName, 
                                                Function<String, R> valueMapper, Consumer<R> targetSetter) {
        getFieldValue(parameters, fieldName)
                .map(valueMapper)
                .ifPresent(targetSetter);
    }
} 