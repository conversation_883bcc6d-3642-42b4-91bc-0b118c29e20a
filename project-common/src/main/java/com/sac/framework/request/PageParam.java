package com.sac.framework.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@Builder@AllArgsConstructor
@NoArgsConstructor
public class PageParam implements Serializable {

    private static final Integer PAGE_NO = 1;
    private static final Integer LIMIT = 10;

    /**
     * 每页条数 - 不分页
     *
     */
    public static final Integer PAGE_SIZE_NONE = -1;

    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码最小值为 1")
    private Integer pageNo = PAGE_NO;

    @NotNull(message = "每页条数不能为空")
    @Min(value = 1, message = "每页条数最小值为 1")
    @Max(value = 100, message = "每页条数最大值为 100")
    private Integer limit = LIMIT;

    private Boolean needPage;

}
