package com.sac.framework.response;

import cn.hutool.core.util.ReflectUtil;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

public class BaseResponseFactory {

    public static <T> BaseResponse<T> build(Class<?> resultType, Integer status, String reasonCode, String errorText) {
        BaseResponse<T> result = (BaseResponse) ReflectUtil.newInstanceIfPossible(resultType);
        result.setState(status);
        result.setCause(errorText);
        Map<String, Object> variables = result.getVariables();
        if (Objects.isNull(variables)) {
            result.setVariables(new HashMap<>());
        }
        result.addVariable("reasonCode", reasonCode);
        return result;
    }

}
