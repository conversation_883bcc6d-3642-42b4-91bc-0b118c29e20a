package com.sac.framework.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;

import java.io.Serializable;
import java.util.List;

@ApiModel(value = "分页列表对象")
@Builder
public class PageList<E> implements Serializable {

	/**
	 * serialVersionUID:
	 */
	private static final long serialVersionUID = -2031192118119239999L;

	@ApiModelProperty(value = "分页数据列表")
	private List<E> dataResult;
	
	@ApiModelProperty(value = "分页结果")
	private PageResult pageResult;
	
	public List<E> getDataResult() {
		return dataResult;
	}
	
	public void setDataResult(List<E> dataResult) {
		this.dataResult = dataResult;
	}

	public PageResult getPageResult() {
		return pageResult;
	}

	public void setPageResult(PageResult pageResult) {
		this.pageResult = pageResult;
	}
	
}
