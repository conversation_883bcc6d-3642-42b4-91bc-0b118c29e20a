package com.sac.framework.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;

import java.io.Serializable;

@ApiModel(value = "分页对象")
@Builder
public class PageResult implements Serializable {

	/**
	 * serialVersionUID:
	 */
	private static final long serialVersionUID = 657406105094323352L;

	public PageResult() {
	}

	public PageResult(int page, int limit, int totalCount) {
		this.page = page;
		this.limit = limit;
		this.totalCount = totalCount;
	}
	
	/**
	 * 分页大小
	 */
	@ApiModelProperty(value = "分页大小")
	private Integer limit;
	
	/**
	 * 页数
	 */
	@ApiModelProperty(value = "页数")
	private Integer page;
	
	/**
	 * 总记录数
	 */
	@ApiModelProperty(value = "总记录数")
	private Integer totalCount;
	
	public Integer getLimit() {
		return limit;
	}
	
	public void setLimit(int limit) {
		this.limit = limit;
	}
	
	public Integer getPage() {
		return page;
	}
	
	public void setPage(int page) {
		this.page = page;
	}
	
	public Integer getTotalCount() {
		return totalCount;
	}
	
	public void setTotalCount(int totalCount) {
		this.totalCount = totalCount;
	}

	@ApiModelProperty(value = "总页数")
	public Integer getTotalPages() {
		if (totalCount <= 0) {
			return 0;
		}
		if (limit <= 0) {
			return 0;
		}

		int count = totalCount / limit;
		if (totalCount % limit > 0) {
			count++;
		}
		return count;
	}
	
}
