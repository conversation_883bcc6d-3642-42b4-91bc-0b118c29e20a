package com.sac.framework.utils;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * <AUTHOR>
 * @date 2020/5/27 9:52
 * @description SHA256 单向散列函数 指纹 消息摘要算法 哈希函数值为32个字节
 * @copyright 2020 南京华盾电力信息安全测评有限公司 All rights reserved.
 */
public class Sha256Util {

    /**
     　　* 利用java原生的摘要实现SHA256加密
     　　* @param bytes 加密后的报文
     　　* @return
     　　*/
    public static String sha256Hex(byte[] bytes){
        MessageDigest messageDigest;
        String encodeStr = "";
        try {
            messageDigest = MessageDigest.getInstance("SHA-256");
            messageDigest.update(bytes);
            encodeStr = byte2Hex(messageDigest.digest());
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        return encodeStr;
    }

    /**
     　　* 将byte转为16进制
     　　* @param bytes
     　　* @return
     　　*/
    private static String byte2Hex(byte[] bytes){
        StringBuffer stringBuffer = new StringBuffer();
        String temp = null;
        for (int i=0;i<bytes.length;i++){
            temp = Integer.toHexString(bytes[i] & 0xFF);
            if (temp.length()==1){
                //1得到一位的进行补0操作
                stringBuffer.append("0");
            }
            stringBuffer.append(temp);
        }
        return stringBuffer.toString();
    }
}
