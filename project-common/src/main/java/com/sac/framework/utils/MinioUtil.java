package com.sac.framework.utils;

import io.minio.MinioClient;
import io.minio.PutObjectOptions;
import io.minio.Result;
import io.minio.errors.ErrorResponseException;
import io.minio.errors.MinioException;
import io.minio.messages.Item;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.regex.Pattern;

@Component
public class MinioUtil {
    @Autowired
    private MinioClient minioClient;

    public MinioUtil(
            @Value("${minio.endPoint}") String endpoint,
            @Value("${minio.username}") String username,
            @Value("${minio.password}") String password
    ) throws Exception {
        MinioXmlParseFixer.fixItemOwnerRequired();
        this.minioClient = new MinioClient(endpoint, username, password);
    }

    private static final Pattern INVALID_CHAR_PATTERN = Pattern.compile("[^a-zA-Z0-9_\\-./]");

    /**
     * 检查存储桶是否存在
     */
    public boolean bucketExists(String bucketName) throws Exception {
        try {
            return minioClient.bucketExists(bucketName);
        } catch (MinioException e) {
            throw new Exception("检查存储桶存在性失败: " + e.getMessage());
        }
    }

    /**
     * 创建存储桶
     */
    public void createBucket(String bucketName) throws Exception {
        if (!bucketExists(bucketName)) {
            try {
                minioClient.makeBucket(bucketName);
            } catch (MinioException e) {
                throw new Exception("创建存储桶失败: " + e.getMessage());
            }
        }
    }

    /**
     * 获取文件输入流
     */
    public InputStream getObject(String bucketName, String objectName) throws Exception {
        try {
            return minioClient.getObject(bucketName, objectName);
        } catch (MinioException e) {
            throw new Exception("获取文件失败: " + e.getMessage());
        }
    }

    /**
     * 上传文件
     */
    public void putObject(String bucketName, String objectName, InputStream inputStream,
                          long size, String contentType) throws Exception {
        try {
            PutObjectOptions options = new PutObjectOptions(size, -1);
            options.setContentType(contentType);
            minioClient.putObject(bucketName, objectName, inputStream, options);
        } catch (MinioException e) {
            throw new Exception("上传文件失败: " + e.getMessage());
        }
    }

    public boolean objectExists(String bucketName, String objectName) throws Exception {
        try {
            minioClient.statObject(bucketName, objectName);
            return true;
        } catch (ErrorResponseException e) {
            if (e.errorResponse().errorCode().code().equals("NoSuchKey")) {
                return false;
            }
            throw e;
        } catch (MinioException e) {
            throw new Exception("检查对象存在性失败: " + e.getMessage());
        }
    }

    public boolean pathExists(String bucketName, String path) throws Exception {
        try {
            Iterable<Result<Item>> results = minioClient.listObjects(bucketName, path, true);
            Iterator<Result<Item>> iterator = results.iterator();
            return iterator.hasNext();
        } catch (Exception e) {
            throw new Exception("检查路径存在性失败: " + e.getMessage());
        }
    }

    /**
     * 获取目录下的所有子目录
     */
    public List<String> listSubDirectories(String bucketName, String parentDir) throws Exception {
        List<String> directories = new ArrayList<>();
        if (!parentDir.endsWith("/")) {
            parentDir += "/";
        }
        try {
            Iterable<Result<Item>> results = minioClient.listObjects(
                    bucketName, parentDir, false);
            for (Result<Item> result : results) {
                Item item = result.get();
                if (item.isDir()) {
                    directories.add(item.objectName());
                }
            }
        } catch (MinioException e) {
            throw new Exception("列出子目录失败: " + e.getMessage());
        }
        return directories;
    }

    /**
     * 将输入流转换为字节数组
     */
    public byte[] inputStreamToByteArray(InputStream inputStream) throws IOException {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[1024];
            int length;
            while ((length = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, length);
            }
            return outputStream.toByteArray();
        } finally {
            if (inputStream != null) {
                inputStream.close();
            }
        }
    }

    public List<String> listObjects(String bucketName, String prefix) throws Exception {
        List<String> objectNames = new ArrayList<>();
        try {
            Iterable<Result<Item>> results = minioClient.listObjects(bucketName, prefix, true);
            for (Result<Item> result : results) {
                Item item = result.get();
                objectNames.add(item.objectName());
                System.out.println("找到对象：" + item.objectName());
            }
        } catch (MinioException e) {
            throw new Exception("列出对象失败: " + e.getMessage());
        }
        return objectNames;
    }

    public void createDirectory(String bucketName, String directoryPath) {
        if (INVALID_CHAR_PATTERN.matcher(directoryPath).find()) {
            throw new IllegalArgumentException("目录路径包含无效字符，仅允许字母、数字、_、-、.、/");
        }
        String normalizedDir = directoryPath.endsWith("/") ? directoryPath : directoryPath + "/";
        normalizedDir = normalizedDir.replaceAll("//+", "/").replaceAll("^/", "");
        String tempFileName = ".tmp";
        String tempFilePath = normalizedDir + tempFileName;
        try {
            PutObjectOptions options = new PutObjectOptions(0, -1);
            options.setContentType("binary/octet-stream");
            minioClient.putObject(
                    bucketName,
                    tempFilePath,
                    new ByteArrayInputStream(new byte[0]),
                    options
            );
            minioClient.removeObject(bucketName, tempFilePath);
        } catch (Exception e) {
            throw new RuntimeException("创建目录失败！目标目录：" + normalizedDir + "，临时文件：" + tempFilePath, e);
        }
    }
}
