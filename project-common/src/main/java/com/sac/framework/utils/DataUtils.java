package com.sac.framework.utils;

import com.sac.framework.constant.Constant;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * DataUtils
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2025/8/15 15:55
 */
public class DataUtils {

    public static List<String> strConvertList(String str) {
        return Arrays.stream(str.split(Constant.COMMA))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .collect(Collectors.toList());
    }

    public static int removeVPrefixAndConvertToInt(String version) {
        if (StringUtils.isBlank(version)) {
            return 0;
        }

        String trimmedVersion = version.trim();
        if (trimmedVersion.startsWith("v") || trimmedVersion.startsWith("V")) {
            String numberPart = trimmedVersion.substring(1);
            if (StringUtils.isEmpty(numberPart)) {
                return 0;
            }
            try {
                return Integer.parseInt(numberPart);
            } catch (NumberFormatException e) {
                return 0;
            }
        } else {
            try {
                return Integer.parseInt(trimmedVersion);
            } catch (NumberFormatException e) {
                return 0;
            }
        }
    }
}
