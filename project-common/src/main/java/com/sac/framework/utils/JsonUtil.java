package com.sac.framework.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

/**
 * @author:timo
 * @date: 2018/9/5
 * @time: 13:00
 * @description: json 工具类
 */
public class JsonUtil {
    private static ObjectMapper objectMapper = new ObjectMapper();

    /**
     * javaBean、列表数组转换为json字符串
     */
    public static String obj2json(Object obj) {
        String str = null;
        try {
            str = objectMapper.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return str;
    }

    /**
     * 与javaBean json数组字符串转换为列表
     */
    public static <T> List<T> json2list(String jsonArrayStr, Class<T> clazz) {
        JavaType javaType = getCollectionType(ArrayList.class, clazz);
        List<T> list = null;
        try {
            list = objectMapper.readValue(jsonArrayStr, javaType);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return list;
    }

    /**
     * 获取泛型的Collection Type
     *
     * @param collectionClass 泛型的Collection
     * @param elementClasses  元素类
     * @return JavaType Java类型
     * @since 1.0
     */
    private static JavaType getCollectionType(Class<?> collectionClass, Class<?>... elementClasses) {
        return objectMapper.getTypeFactory().constructParametricType(collectionClass, elementClasses);
    }

    /**
     * 设置JSON导出响应头
     *
     * @param response  响应对象
     * @param fileName  文件名
     */
    public static void setExportResponseHeader(HttpServletResponse response, String fileName) throws IOException {
        response.setContentType("application/json");
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());
        String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.name())
                .replaceAll("\\+", "%20");
        response.setHeader("Content-Disposition",
                "attachment;filename*=utf-8''" + encodedFileName + ".json");
    }

    /**
     * 导出JSON文件
     *
     * @param response  响应对象
     * @param data      导出数据列表
     * @param clazz     数据类型
     * @param fileName  文件名
     */
    public static <T> void exportJson(HttpServletResponse response, List<T> data, Class<T> clazz, String fileName)
            throws IOException {
        setExportResponseHeader(response, fileName);
        try (OutputStream os = response.getOutputStream();
             BufferedWriter writer = new BufferedWriter(
                     new OutputStreamWriter(os, StandardCharsets.UTF_8))) {
            objectMapper.writeValue(writer, data);
        }
    }

    /**
     * 从输入流读取JSON并解析为对象列表
     *
     * @param inputStream  JSON文件输入流
     * @param clazz        目标对象类型
     * @return 解析后的对象列表
     */
    public static <T> List<T> readJsonByStream(InputStream inputStream, Class<T> clazz) {
        try (InputStreamReader reader = new InputStreamReader(inputStream, StandardCharsets.UTF_8)) {
            JavaType javaType = objectMapper.getTypeFactory().constructCollectionType(List.class, clazz);
            return objectMapper.readValue(reader, javaType);
        } catch (IOException e) {
            throw new RuntimeException("JSON文件解析失败: " + e.getMessage(), e);
        }
    }

    /**
     * 对象转换为JSON字符串
     */
    public static <T> String toJson(T obj) {
        try {
            return objectMapper.writeValueAsString(obj);
        } catch (Exception e) {
            throw new RuntimeException("对象转JSON失败", e);
        }
    }

    /**
     * JSON字符串转换为对象
     */
    public static <T> T fromJson(String json, Class<T> clazz) {
        try {
            return objectMapper.readValue(json, clazz);
        } catch (Exception e) {
            throw new RuntimeException("JSON转对象失败", e);
        }
    }
}
