package com.sac.framework.utils;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.sac.framework.constant.Constant;
import org.apache.commons.compress.utils.IOUtils;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public class ExcelUtil {
    private static final String SHEET_NAME = "数据列表";

    /**
     * 设置Excel导出响应头（通用方法）
     *
     * @param response 响应对象
     * @param fileName 文件名（不含后缀）
     */
    public static void setExportResponseHeader(HttpServletResponse response, String fileName) throws IOException {
        // 设置内容类型
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("UTF-8");
        // 处理中文文件名乱码
        String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()).replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + encodedFileName + ".xlsx");
    }

    /**
     * 导出Excel（默认样式：居中对齐）
     *
     * @param response 响应对象
     * @param data     导出数据列表
     * @param clazz    数据类型
     * @param fileName 文件名（不含后缀）
     */
    public static <T> void exportExcel(HttpServletResponse response, List<T> data, Class<T> clazz, String fileName) throws IOException {
        // 设置响应头
        setExportResponseHeader(response, fileName);
        // 设置单元格样式（水平居中）
        WriteCellStyle contentStyle = new WriteCellStyle();
        contentStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        HorizontalCellStyleStrategy styleStrategy = new HorizontalCellStyleStrategy(null, contentStyle);
        // 写入Excel并响应
        EasyExcel.write(response.getOutputStream(), clazz)
                .registerWriteHandler(styleStrategy)
                .sheet("数据列表")
                .doWrite(data);
    }

    /**
     * 读取Excel文件
     *
     * @param filePath 文件路径
     * @param clazz    数据类型
     * @return 解析后的数据列表
     */
    public static <T> List<T> readExcel(String filePath, Class<T> clazz) {
        return EasyExcel.read(filePath)
                .head(clazz)
                .sheet()
                .doReadSync();
    }

    /**
     * 读取Excel流（用于导入接口）
     *
     * @param inputStream 文件输入流
     * @param clazz       数据类型
     * @return 解析后的数据列表
     */
    public static <T> List<T> readExcelByStream(InputStream inputStream, Class<T> clazz) {
        return EasyExcel.read(inputStream)
                .head(clazz)
                .sheet()
                .doReadSync();
    }

    /**
     * CSV动态导出
     *
     * @param response response
     * @param data     List<Object>为一行数据
     * @param headList 列名
     * @param fileName CSV名称
     * @throws IOException IOException
     */
    public static void exportDynamicColumn(HttpServletResponse response, List<List<String>> data,
                                           List<String> headList, String fileName)
            throws IOException {
        // 设置响应头
        setCsvResponseHeader(response, fileName);
        try (BufferedWriter writer = new BufferedWriter(
                new OutputStreamWriter(response.getOutputStream(), StandardCharsets.UTF_8))) {
            writer.write(String.join(",", headList));
            writer.newLine();
            for (List<String> item : data) {
                writer.write(String.join(",", item));
                writer.newLine();
            }
        }
    }

    public static <T> String manualObjToCsv(List<T> data, Class<T> clazz) throws IllegalAccessException {
        if (CollectionUtils.isEmpty(data)) {
            return StringUtils.EMPTY;
        }
        Field[] fields = clazz.getDeclaredFields();
        List<String> headList = getExcelPropertyNames(fields);
        List<List<String>> dataList = new ArrayList<>();
        for (T item : data) {
            List<String> values = getFieldValues(item, fields);
            dataList.add(values);
        }
        return getCsvString(headList, dataList);
    }

    private static String getCsvString(List<String> headList, List<List<String>> dataList) {
        StringBuilder csv = new StringBuilder();
        // 添加数据头
        String head = String.join(Constant.COMMA, headList);
        csv.append(head).append(Constant.NEW_LINE);
        // 添加数据行
        for (List<String> data : dataList) {
            String dataLine = String.join(Constant.COMMA, data);
            csv.append(dataLine).append(Constant.NEW_LINE);
        }
        return csv.toString();
    }

    private static String escapeCsvField(String field) {
        if (field == null) {
            return "";
        }
        boolean needQuote = field.contains(",")
                || field.contains("\"")
                || field.contains("\n")
                || field.contains("\r");

        if (!needQuote) {
            return field;
        }
        return "\"" + field.replace("\"", "\"\"") + "\"";
    }

    /**
     * 设置CSV导出响应头
     */
    public static void setCsvResponseHeader(HttpServletResponse response, String fileName) throws IOException {
        response.setContentType("text/csv");
        response.setCharacterEncoding("UTF-8");
        String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()).replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + encodedFileName + ".csv");
    }

    /**
     * 导出CSV文件
     *
     * @param response 响应对象
     * @param data     导出数据列表
     * @param clazz    数据类型
     * @param fileName 文件名（不含后缀）
     */
    public static <T> void exportCsv(HttpServletResponse response, List<T> data, Class<T> clazz, String fileName) throws IOException, IllegalAccessException {
        setCsvResponseHeader(response, fileName);
        Field[] fields = clazz.getDeclaredFields();
        List<String> fieldNames = getExcelPropertyNames(fields);
        try (BufferedWriter writer = new BufferedWriter(
                new OutputStreamWriter(response.getOutputStream(), StandardCharsets.UTF_8))) {
            writer.write(String.join(",", fieldNames));
            writer.newLine();
            for (T item : data) {
                List<String> values = getFieldValues(item, fields);
                writer.write(String.join(",", values));
                writer.newLine();
            }
        }
    }

    public static void exportCsv(HttpServletResponse response, InputStream inputStream, String fileName) throws IOException, IllegalAccessException {
        setCsvResponseHeader(response, fileName);
        // 将输入流复制到响应输出流
        try (OutputStream outputStream = response.getOutputStream()) {
            IOUtils.copy(inputStream, outputStream);
        } finally {
            inputStream.close();
        }
    }

    /**
     * 获取ExcelProperty注解的字段名（作为CSV表头）
     */
    private static List<String> getExcelPropertyNames(Field[] fields) {
        return Arrays.asList(fields).stream()
                .filter(field -> field.isAnnotationPresent(com.alibaba.excel.annotation.ExcelProperty.class))
                .map(field -> {
                    com.alibaba.excel.annotation.ExcelProperty annotation = field.getAnnotation(com.alibaba.excel.annotation.ExcelProperty.class);
                    return annotation.value().length > 0 ? annotation.value()[0] : field.getName();
                })
                .collect(Collectors.toList());
    }

    /**
     * 获取对象字段值（处理特殊字符）
     */
    private static <T> List<String> getFieldValues(T item, Field[] fields) throws IllegalAccessException {
        return Arrays.asList(fields).stream()
                .filter(field -> field.isAnnotationPresent(com.alibaba.excel.annotation.ExcelProperty.class))
                .map(field -> {
                    try {
                        field.setAccessible(true);
                        Object value = field.get(item);
                        return formatCsvValue(value);
                    } catch (IllegalAccessException e) {
                        return "";
                    }
                })
                .collect(Collectors.toList());
    }

    /**
     * 格式化CSV值（处理逗号、引号和换行）
     */
    private static String formatCsvValue(Object value) {
        if (value == null) {
            return "";
        }
        String str = value.toString();
        // 如果包含逗号、引号或换行符，需要用双引号包裹
        if (str.contains(",") || str.contains("\"") || str.contains("\n") || str.contains("\r")) {
            // 替换双引号为两个双引号
            str = str.replace("\"", "\"\"");
            return "\"" + str + "\"";
        }
        return str;
    }

    public static <T> List<T> readCsvByStream(InputStream inputStream, Class<T> clazz) {
        List<T> resultList = new ArrayList<>();
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(inputStream, StandardCharsets.UTF_8));
             CSVParser csvParser = new CSVParser(reader,
                     CSVFormat.DEFAULT.withFirstRecordAsHeader().withIgnoreHeaderCase().withTrim())) {
            Field[] fields = clazz.getDeclaredFields();
            for (CSVRecord record : csvParser) {
                T obj = clazz.getDeclaredConstructor().newInstance();
                for (Field field : fields) {
                    field.setAccessible(true);
                    String fieldName = field.getName();
                    String value = record.get(fieldName);
                    if (value != null) {
                        setFieldValue(obj, field, value);
                    }
                }
                resultList.add(obj);
            }
        } catch (Exception e) {
            throw new RuntimeException("CSV文件解析失败", e);
        }
        return resultList;
    }

    private static <T> void setFieldValue(T obj, Field field, String value) throws Exception {
        Class<?> fieldType = field.getType();
        if (fieldType == String.class) {
            field.set(obj, value);
        } else if (fieldType == Integer.class || fieldType == int.class) {
            field.set(obj, Integer.parseInt(value));
        } else if (fieldType == Long.class || fieldType == long.class) {
            field.set(obj, Long.parseLong(value));
        } else if (fieldType == Boolean.class || fieldType == boolean.class) {
            field.set(obj, Boolean.parseBoolean(value));
        } else if (fieldType == Double.class || fieldType == double.class) {
            field.set(obj, Double.parseDouble(value));
        } else if (fieldType == Float.class || fieldType == float.class) {
            field.set(obj, Float.parseFloat(value));
        }
    }
}
