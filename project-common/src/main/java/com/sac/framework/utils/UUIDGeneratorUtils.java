package com.sac.framework.utils;

import java.util.UUID;

/**
 * UUIDGeneratorUtils
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2025/8/5 14:20
 */
public class UUIDGeneratorUtils {
    /**
     * 生成标准UUID (带横线)
     * 格式示例: 550e8400-e29b-41d4-a716-************
     */
    public static String generateStandardUUID() {
        return UUID.randomUUID().toString();
    }

    /**
     * 生成不带横线的UUID
     * 格式示例: 550e8400e29b41d4a716************
     */
    public static String generateUUIDWithoutHyphens() {
        return UUID.randomUUID().toString().replaceAll("-", "");
    }

    /**
     * 生成大写格式的UUID (不带横线)
     * 格式示例: 550E8400E29B41D4A716************
     */
    public static String generateUpperCaseUUID() {
        return UUID.randomUUID().toString().replaceAll("-", "").toUpperCase();
    }
}
