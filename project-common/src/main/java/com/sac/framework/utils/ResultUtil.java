package com.sac.framework.utils;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lc.ibps.api.base.constants.StateEnum;
import com.sac.framework.response.BaseResponse;
import com.sac.framework.response.PageList;
import com.sac.framework.response.PageResult;

public class ResultUtil {
    public static<T> BaseResponse<PageList<T>> success(IPage<T> page) {
        PageResult pageResult = new PageResult((int)page.getCurrent(), (int)page.getSize(), (int)page.getTotal());
        PageList<T> pageList =  PageList.<T>builder()
                .dataResult(page.getRecords())
                .pageResult(pageResult)
                .build();
        BaseResponse<PageList<T>> baseResponse = BaseResponse.<PageList<T>>builder()
                .data(pageList)
                .state(StateEnum.SUCCESS.getCode())
                .build();
        return baseResponse;
    }

    public static<T> BaseResponse<PageList<T>> success(PageList<T> pageList) {
        BaseResponse<PageList<T>> baseResponse = BaseResponse.<PageList<T>>builder()
                .data(pageList)
                .state(StateEnum.SUCCESS.getCode())
                .build();
        return baseResponse;
    }

    public static<T> BaseResponse<T> success(T data) {
        BaseResponse<T> baseResponse = BaseResponse.<T>builder()
                .data(data)
                .state(StateEnum.SUCCESS.getCode())
                .build();
        return baseResponse;
    }

    public static BaseResponse success() {
        BaseResponse baseResponse = BaseResponse.builder()
                .state(StateEnum.SUCCESS.getCode())
                .build();
        return baseResponse;
    }

    public static BaseResponse error(String message) {
        BaseResponse baseResponse = BaseResponse.builder()
                .message(message)
                .state(StateEnum.ERROR.getCode())
                .build();
        return baseResponse;
    }

    public static BaseResponse error() {
        BaseResponse baseResponse = BaseResponse.builder()
                .message("操作失败")
                .state(StateEnum.ERROR.getCode())
                .build();
        return baseResponse;
    }
}
