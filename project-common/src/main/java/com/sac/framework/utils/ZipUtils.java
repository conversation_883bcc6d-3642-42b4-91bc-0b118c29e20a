package com.sac.framework.utils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipOutputStream;

public class ZipUtils {
    /**
     * 创建ZIP文件
     */
    public static byte[] createZip(List<ZipEntryInfo> entries) throws IOException {
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream();
             ZipOutputStream zos = new ZipOutputStream(baos)) {
            for (ZipEntryInfo entry : entries) {
                ZipEntry zipEntry = new ZipEntry(entry.getEntryName());
                zos.putNextEntry(zipEntry);
                zos.write(entry.getContent());
                zos.closeEntry();
            }
            zos.finish();
            return baos.toByteArray();
        }
    }

    /**
     * 解析ZIP文件
     */
    public static List<ZipEntryInfo> parseZip(InputStream inputStream) throws IOException {
        List<ZipEntryInfo> entries = new ArrayList<>();
        try (ZipInputStream zis = new ZipInputStream(inputStream)) {
            ZipEntry entry;
            while ((entry = zis.getNextEntry()) != null) {
                if (!entry.isDirectory()) {
                    byte[] content = readInputStream(zis);
                    entries.add(new ZipEntryInfo(entry.getName(), content));
                }
                zis.closeEntry();
            }
        }
        return entries;
    }

    /**
     * 读取输入流内容
     */
    private static byte[] readInputStream(InputStream inputStream) throws IOException {
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[1024];
            int len;
            while ((len = inputStream.read(buffer)) != -1) {
                baos.write(buffer, 0, len);
            }
            return baos.toByteArray();
        }
    }

    /**
     * ZIP条目信息类
     */
    public static class ZipEntryInfo {
        private String entryName;
        private byte[] content;

        public ZipEntryInfo(String entryName, byte[] content) {
            this.entryName = entryName;
            this.content = content;
        }

        public String getEntryName() {
            return entryName;
        }

        public byte[] getContent() {
            return content;
        }
    }
}