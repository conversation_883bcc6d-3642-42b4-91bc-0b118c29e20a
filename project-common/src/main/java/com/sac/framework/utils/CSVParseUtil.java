package com.sac.framework.utils;

import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;

public class CSVParseUtil {
    public static CSVParseData parse(MultipartFile file) {
        try {
            return parseInputStream(file.getInputStream());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public static CSVParseData parseInputStream(InputStream inputStream) {
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(inputStream, "GBK"));
             CSVParser csvParser = new CSVParser(reader,
                     CSVFormat.DEFAULT
                             .withFirstRecordAsHeader()
                             .withIgnoreHeaderCase()
                             .withTrim())) {
            List<String> columnNames = csvParser.getHeaderNames();
            List<List<String>> dataRows = new ArrayList<>();
            for (CSVRecord record : csvParser) {
                List<String> row = new ArrayList<>();
                for (String column : columnNames) {
                    row.add(record.get(column) != null ? record.get(column) : "");
                }
                dataRows.add(row);
            }
            return new CSVParseData(columnNames, dataRows);
        } catch (Exception e) {
            throw new RuntimeException("CSV文件解析失败: " + e.getMessage(), e);
        }
    }
}
