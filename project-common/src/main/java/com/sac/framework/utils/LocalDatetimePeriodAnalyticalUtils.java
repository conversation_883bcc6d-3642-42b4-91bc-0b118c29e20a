package com.sac.framework.utils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Arrays;
import java.util.List;

import com.sac.framework.exception.ArgumentException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * @author: JSQ
 * @date: 2/1/2020 上午11:27
 * @description: 通用时间取值周期解析工具类
 * @copyright: 2020 南京华盾电力信息安全测评有限公司 All rights reserved.
 */
public class LocalDatetimePeriodAnalyticalUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(LocalDatetimePeriodAnalyticalUtils.class);

    private static final List<String> baseDatetimes = Arrays.asList(new String[]{"@year", "@month", "@today", "@now", "@yesterday", "@yesterdaynow", "@lastyeartoday", "@lastyearnow"});

    /**
     * 处理具体配置
     *
     * <AUTHOR>
     * @param param 具体配置字符串
     * @return 处理结果
     * @throws {@link IllegalArgumentException}
     */
    public static LocalDateTime handleLocalDatetimeParam(String param) throws ArgumentException {
        OperatorsEnum operator = null;
        LocalDateTime result;
        String[] params = new String[0];
        if (param.indexOf('-') > 0) {
            operator = OperatorsEnum.SUBTRACT;
            params = param.split("\\-");
        }else if (param.indexOf('+') > 0) {
            operator = OperatorsEnum.PLUS;
            params = param.split("\\+");
        }
        if (params.length > 2) {
            throw new ArgumentException("时间配置错误" + param);
        }
        //无操作符
        if (params.length == 0 || params.length ==1) {
            result = getBaseDateTime(param.trim());
        }else {
            result = getBaseDateTime(params[0].trim());
            result = computeDeviation(result, operator, params[1].trim());
        }
        return result;
    }


    /**
     * 计算基准时间
     *
     * <AUTHOR>
     * @param
     * @param
     * @return
     * @throws
     */
    private static LocalDateTime getBaseDateTime(String param) throws ArgumentException {
        if (!baseDatetimes.contains(param)) {
            throw new ArgumentException("时间配置错误：" + param);
        }
        LocalDateTime result;
        switch (param){
            case "@now":
                result = LocalDateTime.now();
                break;
            case "@hour":
                result = LocalDate.now().atTime(LocalTime.now().getHour(), 0);
            case "@today":
                result = LocalDate.now().atStartOfDay();
                break;
            case "@yesterday":
                result = LocalDate.of(LocalDate.now().getYear(), LocalDate.now().getMonth(), LocalDate.now().getDayOfMonth()-1).atTime(0,0);
                break;
            case "@yesterdaynow":
                result = LocalDate.of(LocalDate.now().getYear(), LocalDate.now().getMonth(), LocalDate.now().getDayOfMonth()-1).atTime(LocalTime.now().getHour(),LocalTime.now().getMinute());
                break;
            case "@month":
                result = LocalDate.of(LocalDate.now().getYear(), LocalDate.now().getMonth(), 1).atTime(0,0);
                break;
            case "@year":
                result = LocalDate.of(LocalDate.now().getYear(), 1, 1).atTime(0,0);
                break;
            case "@lastyeartoday":
                result = LocalDate.of(LocalDate.now().getYear()-1, LocalDate.now().getMonth(), LocalDate.now().getDayOfMonth()).atTime(0,0);
                break;
            case "@lastyearnow":
                result = LocalDate.of(LocalDate.now().getYear()-1, LocalDate.now().getMonth(), LocalDate.now().getDayOfMonth()).atTime(LocalTime.now().getHour(),LocalTime.now().getMinute());
                break;
            default:
                result = LocalDateTime.now();
                break;
        }
        return result;
    }

    /**
     * 计算值偏差
     *
     * <AUTHOR>
     * @param
     * @param
     * @return
     * @throws
     */
    private static LocalDateTime computeDeviation(LocalDateTime baseDatetime, OperatorsEnum operator, String offset) throws ArgumentException {
        if (StringUtils.isBlank(offset)){
            return baseDatetime;
        }
        LocalDateTime result = baseDatetime;
        int offsetValue = 0;
        //分割最后一个字符
        try {
            String offsetValueString= offset.substring(0, offset.length() -1);
            offsetValue = Integer.parseInt(offsetValueString);
            if (operator == OperatorsEnum.SUBTRACT) {
                offsetValue = -offsetValue;
            }
        }catch (Exception e) {
            throw new ArgumentException("测点历史值取值区间配置错误：" + offset);
        }
        String timeFlag = offset.substring(offset.length() -1, offset.length());
        switch (timeFlag) {
            case "y":
                result = baseDatetime.plusYears(offsetValue);
                break;
            case "M":
                result = baseDatetime.plusMonths(offsetValue);
                break;
            case "d":
                result = baseDatetime.plusDays(offsetValue);
                break;
            case "h":
                result = baseDatetime.plusHours(offsetValue);
                break;
            case "m":
                result = baseDatetime.plusMinutes(offsetValue);
                break;
            case "s":
                result = baseDatetime.plusSeconds(offsetValue);
                break;
            default:
                break;
        }
        return result;
    }



    /**
     * 操作符
     */
    enum OperatorsEnum {

        /**
         * +
         */
        PLUS,

        /**
         * -
         */
        SUBTRACT
    }
}
