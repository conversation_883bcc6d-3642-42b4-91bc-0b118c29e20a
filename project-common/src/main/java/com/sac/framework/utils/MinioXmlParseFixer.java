package com.sac.framework.utils;

import org.simpleframework.xml.Element;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Proxy;
import java.util.Map;

public class MinioXmlParseFixer {
    public static void fixItemOwnerRequired() {
        try {
            Class<?> itemClass = Class.forName("io.minio.messages.Item");
            Field ownerField = itemClass.getDeclaredField("owner");
            ownerField.setAccessible(true);
            Element elementAnnotation = ownerField.getAnnotation(Element.class);
            if (elementAnnotation == null) {
                return;
            }
            InvocationHandler handler = Proxy.getInvocationHandler(elementAnnotation);
            Field annotationField = handler.getClass().getDeclaredField("memberValues");
            annotationField.setAccessible(true);
            Map<String, Object> memberValues = (Map<String, Object>) annotationField.get(handler);
            memberValues.put("required", false);
            System.out.println("MinIO Item 类 Owner 字段注解修复完成：required=false");
        } catch (Exception e) {
            System.err.println("修复 MinIO XML 解析失败：" + e.getMessage());
            e.printStackTrace();
        }
    }
}
